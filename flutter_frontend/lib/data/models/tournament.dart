// soccer_frontend/data/models/tournament.dart
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart'; // For @immutable, @Deprecated, listEquals, mapEquals, setEquals
import 'game_timing_config.dart'; // Ensure this is imported
import 'division.dart'; // << NEW IMPORT

@immutable
class Tournament extends Equatable {
  final String? id; // Null for new tournaments
  final String name;
  final String sportType;
  final DateTime startDate;
  final DateTime endDate;
  final String status; // e.g., 'planning', 'registration_open', 'live', 'completed'
  final String? city;
  final String? state;
  final DateTime? registrationDeadline;

  // OLD FIELDS - Mark as deprecated, replaced by divisions
  @Deprecated('Use divisions for age/gender specific settings')
  final String? ageGroups; // Example: "Boys U10, U12 and Girls U14" (deprecated)
  @Deprecated('Use divisions for per-division registration fees')
  final Map<String, double>? registrationFeesByFieldSize; // e.g., {'4v4': 250.0} (deprecated)

  // NEW: Divisions for granular management
  final List<Division>? divisions; // << NEW FIELD

  final int? gamesPerTeam; // This could potentially move to Division level in future
  final String? managingClubId; // If created via an affiliated club
  final String? rules;
  final String? refundPolicy;
  final String? directorName;
  final String? directorEmail;
  final String? directorPhone;
  final String? tournamentFormat;

  // New fields for Additional Info screen
  final String? description;
  final String? facebookUrl;
  final String? twitterUrl;
  final String? instagramUrl;
  final String? websiteUrl;
  final DateTime? earlyBirdDeadline;
  final double? earlyBirdFee;
  final double? lateFee;
  final DateTime? lateRegistrationStart;
  final int? maxTeams;
  // @Deprecated('Game duration is now per age/field in gameTimingConfigurations')
  // final int? gameDurationMinutes; // This field should be removed if it existed as a global setting

  final int? minRosterSize;
  final int? maxRosterSize;
  final String? awards;
  final bool? hasConcessions;
  final bool? hasMerchandise;
  final bool? hasMedical;
  final String? admissionFee;
  final String? parkingInfo;
  final String? spectatorInfo;
  final String? secondaryContactName;
  final String? secondaryContactEmail;
  final String? secondaryContactPhone;
  final String? secondaryContactRole;

  // Game timing configurations map
  final Map<String, GameTimingConfig>? gameTimingConfigurations; // << Existing field

  // Selected venue and field IDs for linking
  final Set<String>? selectedVenueIds;
  final Set<String>? selectedFieldIds;

  const Tournament({
    this.id,
    required this.name,
    required this.sportType,
    required this.startDate,
    required this.endDate,
    required this.status,
    this.city,
    this.state,
    this.registrationDeadline,
    this.ageGroups, // Deprecated but kept for backward compatibility (set by step 1)
    this.registrationFeesByFieldSize, // Deprecated but kept for backward compatibility (set by step 1)
    this.divisions, // << NEW
    this.gamesPerTeam,
    this.managingClubId,
    this.rules,
    this.refundPolicy,
    this.directorName,
    this.directorEmail,
    this.directorPhone,
    this.tournamentFormat,
    this.description,
    this.facebookUrl,
    this.twitterUrl,
    this.instagramUrl,
    this.websiteUrl,
    this.earlyBirdDeadline,
    this.earlyBirdFee,
    this.lateFee,
    this.lateRegistrationStart,
    this.maxTeams,
    // this.gameDurationMinutes, // If this existed globally, ensure it's removed
    this.minRosterSize,
    this.maxRosterSize,
    this.awards,
    this.hasConcessions,
    this.hasMerchandise,
    this.hasMedical,
    this.admissionFee,
    this.parkingInfo,
    this.spectatorInfo,
    this.secondaryContactName,
    this.secondaryContactEmail,
    this.secondaryContactPhone,
    this.secondaryContactRole,
    this.gameTimingConfigurations,
    this.selectedVenueIds,
    this.selectedFieldIds,
  });

  Tournament copyWith({
    String? id,
    String? name,
    String? sportType,
    DateTime? startDate,
    DateTime? endDate,
    String? status,
    String? city,
    String? state,
    DateTime? registrationDeadline,
    @Deprecated('Use divisions instead') String? ageGroups, // Still for backward compatibility
    @Deprecated('Use divisions instead') Map<String, double>? registrationFeesByFieldSize, // Still for backward compatibility
    List<Division>? divisions, // << NEW
    int? gamesPerTeam,
    String? managingClubId,
    String? rules,
    String? refundPolicy,
    String? directorName,
    String? directorEmail,
    String? directorPhone,
    String? tournamentFormat,
    String? description,
    String? facebookUrl,
    String? twitterUrl,
    String? instagramUrl,
    String? websiteUrl,
    DateTime? earlyBirdDeadline,
    double? earlyBirdFee,
    double? lateFee,
    DateTime? lateRegistrationStart,
    int? maxTeams,
    // int? gameDurationMinutes, // If this existed globally, ensure it's removed
    int? minRosterSize,
    int? maxRosterSize,
    String? awards,
    bool? hasConcessions,
    bool? hasMerchandise,
    bool? hasMedical,
    String? admissionFee,
    String? parkingInfo,
    String? spectatorInfo,
    String? secondaryContactName,
    String? secondaryContactEmail,
    String? secondaryContactPhone,
    String? secondaryContactRole,
    Map<String, GameTimingConfig>? gameTimingConfigurations,
    Set<String>? selectedVenueIds,
    Set<String>? selectedFieldIds,
  }) {
    return Tournament(
      id: id ?? this.id,
      name: name ?? this.name,
      sportType: sportType ?? this.sportType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      city: city ?? this.city,
      state: state ?? this.state,
      registrationDeadline: registrationDeadline ?? this.registrationDeadline,
      ageGroups: ageGroups ?? this.ageGroups, // Kept for backward compatibility
      registrationFeesByFieldSize: registrationFeesByFieldSize ?? this.registrationFeesByFieldSize, // Kept for backward compatibility
      divisions: divisions ?? this.divisions, // << NEW
      gamesPerTeam: gamesPerTeam ?? this.gamesPerTeam,
      managingClubId: managingClubId ?? this.managingClubId,
      rules: rules ?? this.rules,
      refundPolicy: refundPolicy ?? this.refundPolicy,
      directorName: directorName ?? this.directorName,
      directorEmail: directorEmail ?? this.directorEmail,
      directorPhone: directorPhone ?? this.directorPhone,
      tournamentFormat: tournamentFormat ?? this.tournamentFormat,
      description: description ?? this.description,
      facebookUrl: facebookUrl ?? this.facebookUrl,
      twitterUrl: twitterUrl ?? this.twitterUrl,
      instagramUrl: instagramUrl ?? this.instagramUrl,
      websiteUrl: websiteUrl ?? this.websiteUrl,
      earlyBirdDeadline: earlyBirdDeadline ?? this.earlyBirdDeadline,
      earlyBirdFee: earlyBirdFee ?? this.earlyBirdFee,
      lateFee: lateFee ?? this.lateFee,
      lateRegistrationStart: lateRegistrationStart ?? this.lateRegistrationStart,
      maxTeams: maxTeams ?? this.maxTeams,
      // gameDurationMinutes: gameDurationMinutes ?? this.gameDurationMinutes, // If removed, this line is gone
      minRosterSize: minRosterSize ?? this.minRosterSize,
      maxRosterSize: maxRosterSize ?? this.maxRosterSize,
      awards: awards ?? this.awards,
      hasConcessions: hasConcessions ?? this.hasConcessions,
      hasMerchandise: hasMerchandise ?? this.hasMerchandise,
      hasMedical: hasMedical ?? this.hasMedical,
      admissionFee: admissionFee ?? this.admissionFee,
      parkingInfo: parkingInfo ?? this.parkingInfo,
      spectatorInfo: spectatorInfo ?? this.spectatorInfo,
      secondaryContactName: secondaryContactName ?? this.secondaryContactName,
      secondaryContactEmail: secondaryContactEmail ?? this.secondaryContactEmail,
      secondaryContactPhone: secondaryContactPhone ?? this.secondaryContactPhone,
      secondaryContactRole: secondaryContactRole ?? this.secondaryContactRole,
      gameTimingConfigurations: gameTimingConfigurations ?? this.gameTimingConfigurations,
      selectedVenueIds: selectedVenueIds ?? this.selectedVenueIds,
      selectedFieldIds: selectedFieldIds ?? this.selectedFieldIds,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'sport_type': sportType,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'status': status,
      'city': city,
      'state': state,
      'registration_deadline': registrationDeadline?.toIso8601String(),
      // Deprecated fields might still be serialized for backward compatibility if backend expects them.
      // For new tournaments, these would be null or replaced by divisions.
      'age_groups': ageGroups, // Deprecated, but included for backward compatibility
      'registration_fees_by_field_size': registrationFeesByFieldSize, // Deprecated, but included for backward compatibility
      'divisions': divisions?.map((d) => d.toJson()).toList(), // << NEW: Serialize divisions
      'games_per_team': gamesPerTeam,
      'managing_club_id': managingClubId,
      'rules': rules,
      'refund_policy': refundPolicy,
      'director_name': directorName,
      'director_email': directorEmail,
      'director_phone': directorPhone,
      'tournament_format': tournamentFormat,
      'description': description,
      'facebook_url': facebookUrl,
      'twitter_url': twitterUrl,
      'instagram_url': instagramUrl,
      'website_url': websiteUrl,
      'early_bird_deadline': earlyBirdDeadline?.toIso8601String(),
      'early_bird_fee': earlyBirdFee,
      'late_fee': lateFee,
      'late_registration_start': lateRegistrationStart?.toIso8601String(),
      'max_teams': maxTeams,
      // 'game_duration_minutes': gameDurationMinutes, // If removed, this line is gone
      'min_roster_size': minRosterSize,
      'max_roster_size': maxRosterSize,
      'awards': awards,
      'has_concessions': hasConcessions,
      'has_merchandise': hasMerchandise,
      'has_medical': hasMedical,
      'admission_fee': admissionFee,
      'parking_info': parkingInfo,
      'spectator_info': spectatorInfo,
      'secondary_contact_name': secondaryContactName,
      'secondary_contact_email': secondaryContactEmail,
      'secondary_contact_phone': secondaryContactPhone,
      'secondary_contact_role': secondaryContactRole,
      // Game timing configurations
      'game_timing_configurations': gameTimingConfigurations?.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
      // Selected venue and field IDs for linking
      'selected_venue_ids': selectedVenueIds?.toList(),
      'selected_field_ids': selectedFieldIds?.toList(),
    };
  }

  factory Tournament.fromJson(Map<String, dynamic> json) {
    return Tournament(
      id: json['id'] as String?,
      name: json['name'] as String,
      sportType: json['sport_type'] as String,
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      status: json['status'] as String,
      city: json['city'] as String?,
      state: json['state'] as String?,
      registrationDeadline: json['registration_deadline'] != null
          ? DateTime.parse(json['registration_deadline'] as String)
          : null,
      ageGroups: json['age_groups'] as String?, // Kept for backward compatibility
      registrationFeesByFieldSize: (json['registration_fees_by_field_size'] as Map<String, dynamic>?)?.map(
        (k, v) => MapEntry(k, (v as num).toDouble()),
      ), // Kept for backward compatibility
      divisions: (json['divisions'] as List<dynamic>?)
          ?.map((d) => Division.fromJson(d as Map<String, dynamic>))
          .toList(), // << NEW: Deserialize divisions
      gamesPerTeam: json['games_per_team'] as int?,
      managingClubId: json['managing_club_id'] as String?,
      rules: json['rules'] as String?,
      refundPolicy: json['refund_policy'] as String?,
      directorName: json['director_name'] as String?,
      directorEmail: json['director_email'] as String?,
      directorPhone: json['director_phone'] as String?,
      tournamentFormat: json['tournament_format'] as String?,
      description: json['description'] as String?,
      facebookUrl: json['facebook_url'] as String?,
      twitterUrl: json['twitter_url'] as String?,
      instagramUrl: json['instagram_url'] as String?,
      websiteUrl: json['website_url'] as String?,
      earlyBirdDeadline: json['early_bird_deadline'] != null
          ? DateTime.parse(json['early_bird_deadline'] as String)
          : null,
      earlyBirdFee: (json['early_bird_fee'] as num?)?.toDouble(),
      lateFee: (json['late_fee'] as num?)?.toDouble(),
      lateRegistrationStart: json['late_registration_start'] != null
          ? DateTime.parse(json['late_registration_start'] as String)
          : null,
      maxTeams: json['max_teams'] as int?,
      // gameDurationMinutes: json['game_duration_minutes'] as int?, // If removed, this line is gone
      minRosterSize: json['min_roster_size'] as int?,
      maxRosterSize: json['max_roster_size'] as int?,
      awards: json['awards'] as String?,
      hasConcessions: json['has_concessions'] as bool?,
      hasMerchandise: json['has_merchandise'] as bool?,
      hasMedical: json['has_medical'] as bool?,
      admissionFee: json['admission_fee'] as String?,
      parkingInfo: json['parking_info'] as String?,
      spectatorInfo: json['spectator_info'] as String?,
      secondaryContactName: json['secondary_contact_name'] as String?,
      secondaryContactEmail: json['secondary_contact_email'] as String?,
      secondaryContactPhone: json['secondary_contact_phone'] as String?,
      secondaryContactRole: json['secondary_contact_role'] as String?,
      // Game timing configurations
      gameTimingConfigurations: (json['game_timing_configurations'] as Map<String, dynamic>?)?.map(
        (key, value) => MapEntry(key, GameTimingConfig.fromJson(value as Map<String, dynamic>)),
      ),
      // Selected venue and field IDs for linking
      selectedVenueIds: (json['selected_venue_ids'] as List<dynamic>?)?.map((e) => e as String).toSet(),
      selectedFieldIds: (json['selected_field_ids'] as List<dynamic>?)?.map((e) => e as String).toSet(),
    );
  }

  @override
  List<Object?> get props => [
        id, name, sportType, startDate, endDate, status, city, state,
        registrationDeadline, ageGroups, registrationFeesByFieldSize, // Keep for backward compatibility
        listEquals(divisions, null) ? null : Object.hashAll(divisions!), // Handle null for list hashcode
        gamesPerTeam, managingClubId, rules, refundPolicy, directorName,
        directorEmail, directorPhone, tournamentFormat, description, facebookUrl,
        twitterUrl, instagramUrl, websiteUrl, earlyBirdDeadline, earlyBirdFee,
        lateFee, lateRegistrationStart, maxTeams,
        minRosterSize, maxRosterSize, awards, hasConcessions, hasMerchandise,
        hasMedical, admissionFee, parkingInfo, spectatorInfo,
        secondaryContactName, secondaryContactEmail, secondaryContactPhone,
        secondaryContactRole,
        mapEquals(gameTimingConfigurations, null) ? null : Object.hashAll(gameTimingConfigurations!.entries), // Handle null for map hashcode
        selectedVenueIds, selectedFieldIds,
      ];

  // Manual comparison for deprecated map equality in props for deep comparison
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    // Ensure comparison against same runtimeType if Equatable is not handled by base class
    // Equatable handles runtimeType internally, so usually just `other is Tournament` is fine.
    return other is Tournament &&
        id == other.id &&
        name == other.name &&
        sportType == other.sportType &&
        startDate == other.startDate &&
        endDate == other.endDate &&
        status == other.status &&
        city == other.city &&
        state == other.state &&
        registrationDeadline == other.registrationDeadline &&
        // For deprecated maps, use deep equality check provided by flutter/foundation.dart
        mapEquals(registrationFeesByFieldSize, other.registrationFeesByFieldSize) &&
        listEquals(divisions, other.divisions) && // Use listEquals for divisions
        gamesPerTeam == other.gamesPerTeam &&
        managingClubId == other.managingClubId &&
        rules == other.rules &&
        refundPolicy == other.refundPolicy &&
        directorName == other.directorName &&
        directorEmail == other.directorEmail &&
        directorPhone == other.directorPhone &&
        tournamentFormat == other.tournamentFormat &&
        description == other.description &&
        facebookUrl == other.facebookUrl &&
        twitterUrl == other.twitterUrl &&
        instagramUrl == other.instagramUrl &&
        websiteUrl == other.websiteUrl &&
        earlyBirdDeadline == other.earlyBirdDeadline &&
        earlyBirdFee == other.earlyBirdFee &&
        lateFee == other.lateFee &&
        lateRegistrationStart == other.lateRegistrationStart &&
        maxTeams == other.maxTeams &&
        minRosterSize == other.minRosterSize &&
        maxRosterSize == other.maxRosterSize &&
        awards == other.awards &&
        hasConcessions == other.hasConcessions &&
        hasMerchandise == other.hasMerchandise &&
        hasMedical == other.hasMedical &&
        admissionFee == other.admissionFee &&
        parkingInfo == other.parkingInfo &&
        spectatorInfo == other.spectatorInfo &&
        secondaryContactName == other.secondaryContactName &&
        secondaryContactEmail == other.secondaryContactEmail &&
        secondaryContactPhone == other.secondaryContactPhone &&
        secondaryContactRole == other.secondaryContactRole &&
        mapEquals(gameTimingConfigurations, other.gameTimingConfigurations) && // Use mapEquals for timing configs
        setEquals(selectedVenueIds, other.selectedVenueIds) &&
        setEquals(selectedFieldIds, other.selectedFieldIds);
  }

  @override
  int get hashCode => Object.hashAll([
        id, name, sportType, startDate, endDate, status, city, state,
        registrationDeadline,
        ageGroups, // Deprecated but included
        registrationFeesByFieldSize, // Deprecated but included
        // Use Object.hashAll for lists and maps to get a deep hash, handle null lists/maps
        listEquals(divisions, null) ? null : Object.hashAll(divisions!),
        gamesPerTeam, managingClubId, rules, refundPolicy, directorName,
        directorEmail, directorPhone, tournamentFormat, description, facebookUrl,
        twitterUrl, instagramUrl, websiteUrl, earlyBirdDeadline, earlyBirdFee,
        lateFee, lateRegistrationStart, maxTeams, minRosterSize, maxRosterSize,
        awards, hasConcessions, hasMerchandise, hasMedical, admissionFee, parkingInfo,
        spectatorInfo, secondaryContactName, secondaryContactEmail, secondaryContactPhone,
        secondaryContactRole,
        listEquals(gameTimingConfigurations?.entries.toList(), null) ? null : Object.hashAll(gameTimingConfigurations!.entries),
        selectedVenueIds, selectedFieldIds,
      ]);
}

// These functions are imported from 'package:flutter/foundation.dart', so no need to redefine them here.
// bool mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) { ... }
// bool listEquals<T>(List<T>? a, List<T>? b) { ... }