// soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart
import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logging/logging.dart';
import 'package:soccer_frontend/core/services/auth_service.dart';
import 'package:soccer_frontend/data/models/club.dart';
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:soccer_frontend/data/models/venue.dart';
import 'package:soccer_frontend/data/models/field.dart';
import 'package:soccer_frontend/data/models/field_type.dart';
import 'package:soccer_frontend/data/models/game_timing_config.dart';
import 'package:soccer_frontend/data/models/division.dart'; // << NEW IMPORT
import 'package:soccer_frontend/features/tournament/services/tournament_api_service.dart';
// Define all event classes at the top of the file
abstract class CreateTournamentEvent {
  const CreateTournamentEvent();
}

// Initialization event
class InitializeTournamentCreation extends CreateTournamentEvent {
  const InitializeTournamentCreation();
}

// Step 1 events
class TournamentNameChanged extends CreateTournamentEvent {
  final String name;
  const TournamentNameChanged(this.name);
}

class SportTypeChanged extends CreateTournamentEvent {
  final String sportType;
  const SportTypeChanged(this.sportType);
}

class StartDateChanged extends CreateTournamentEvent {
  final DateTime startDate;
  const StartDateChanged(this.startDate);
}

class EndDateChanged extends CreateTournamentEvent {
  final DateTime endDate;
  const EndDateChanged(this.endDate);
}

class Step1Completed extends CreateTournamentEvent {
  final Tournament tournamentDetails;
  const Step1Completed(this.tournamentDetails);
}

// Division management events
class AddDivision extends CreateTournamentEvent {
  final Division division;
  const AddDivision(this.division);
}

class RemoveDivision extends CreateTournamentEvent {
  final String divisionId;
  const RemoveDivision(this.divisionId);
}

class UpdateDivision extends CreateTournamentEvent {
  final Division division;
  const UpdateDivision(this.division);
}

// Venue/Field selection events
class LoadAvailableVenues extends CreateTournamentEvent {
  const LoadAvailableVenues();
}

class ToggleVenueSelection extends CreateTournamentEvent {
  final String venueId;
  final bool isSelected;
  const ToggleVenueSelection(this.venueId, this.isSelected);
}

class LoadFieldTypes extends CreateTournamentEvent {
  const LoadFieldTypes();
}

class LoadFieldsForVenue extends CreateTournamentEvent {
  final String venueId;
  const LoadFieldsForVenue(this.venueId);
}

class ToggleFieldSelection extends CreateTournamentEvent {
  final String venueId;
  final String fieldId;
  final bool isSelected;
  const ToggleFieldSelection(this.venueId, this.fieldId, this.isSelected);
}

class ProceedToNextStepFromVenueFieldSelection extends CreateTournamentEvent {
  final List<String> selectedAgeGroups;
  final List<String> selectedFieldSizes;
  const ProceedToNextStepFromVenueFieldSelection(
    this.selectedAgeGroups,
    this.selectedFieldSizes,
  );
}

// Additional info events
class AdditionalInfoStepCompleted extends CreateTournamentEvent {
  final Tournament tournamentDetails;
  const AdditionalInfoStepCompleted(this.tournamentDetails);
}

class FacebookUrlChanged extends CreateTournamentEvent {
  final String url;
  const FacebookUrlChanged(this.url);
}

class TwitterUrlChanged extends CreateTournamentEvent {
  final String url;
  const TwitterUrlChanged(this.url);
}

class InstagramUrlChanged extends CreateTournamentEvent {
  final String url;
  const InstagramUrlChanged(this.url);
}

class WebsiteUrlChanged extends CreateTournamentEvent {
  final String url;
  const WebsiteUrlChanged(this.url);
}

class TournamentDescriptionChanged extends CreateTournamentEvent {
  final String description;
  const TournamentDescriptionChanged(this.description);
}

class EarlyBirdDeadlineChanged extends CreateTournamentEvent {
  final DateTime deadline;
  const EarlyBirdDeadlineChanged(this.deadline);
}

class EarlyBirdFeeChanged extends CreateTournamentEvent {
  final double fee;
  const EarlyBirdFeeChanged(this.fee);
}

class LateFeeChanged extends CreateTournamentEvent {
  final double fee;
  const LateFeeChanged(this.fee);
}

class LateRegistrationStartChanged extends CreateTournamentEvent {
  final DateTime date;
  const LateRegistrationStartChanged(this.date);
}

class MaxTeamsChanged extends CreateTournamentEvent {
  final int maxTeams;
  const MaxTeamsChanged(this.maxTeams);
}

class MinRosterSizeChanged extends CreateTournamentEvent {
  final int size;
  const MinRosterSizeChanged(this.size);
}

class MaxRosterSizeChanged extends CreateTournamentEvent {
  final int size;
  const MaxRosterSizeChanged(this.size);
}

class AwardsChanged extends CreateTournamentEvent {
  final String awards;
  const AwardsChanged(this.awards);
}

class HasConcessionsChanged extends CreateTournamentEvent {
  final bool value;
  const HasConcessionsChanged(this.value);
}

class HasMerchandiseChanged extends CreateTournamentEvent {
  final bool value;
  const HasMerchandiseChanged(this.value);
}

class HasMedicalChanged extends CreateTournamentEvent {
  final bool value;
  const HasMedicalChanged(this.value);
}

class AdmissionFeeChanged extends CreateTournamentEvent {
  final String fee;
  const AdmissionFeeChanged(this.fee);
}

class ParkingInfoChanged extends CreateTournamentEvent {
  final String info;
  const ParkingInfoChanged(this.info);
}

class SpectatorInfoChanged extends CreateTournamentEvent {
  final String info;
  const SpectatorInfoChanged(this.info);
}

class SecondaryContactNameChanged extends CreateTournamentEvent {
  final String name;
  const SecondaryContactNameChanged(this.name);
}

class SecondaryContactEmailChanged extends CreateTournamentEvent {
  final String email;
  const SecondaryContactEmailChanged(this.email);
}

class SecondaryContactPhoneChanged extends CreateTournamentEvent {
  final String phone;
  const SecondaryContactPhoneChanged(this.phone);
}

class SecondaryContactRoleChanged extends CreateTournamentEvent {
  final String role;
  const SecondaryContactRoleChanged(this.role);
}

// Game timing submission event
class GameTimingsSubmitted extends CreateTournamentEvent {
  final Map<String, GameTimingConfig> gameTimings;
  const GameTimingsSubmitted(this.gameTimings);
}

// Venue management events
class VenueAdded extends CreateTournamentEvent {
  final Venue venue;
  const VenueAdded(this.venue);
}

class VenueUpdated extends CreateTournamentEvent {
  final Venue venue;
  const VenueUpdated(this.venue);
}

class VenueRemoved extends CreateTournamentEvent {
  final Venue venue;
  const VenueRemoved(this.venue);
}

// Field management events  
class FieldAddedToVenue extends CreateTournamentEvent {
  final String venueId;
  final Field field;
  const FieldAddedToVenue(this.venueId, this.field);
}

class FieldUpdatedInVenue extends CreateTournamentEvent {
  final String venueId;
  final Field field;
  const FieldUpdatedInVenue(this.venueId, this.field);
}

class FieldRemovedFromVenue extends CreateTournamentEvent {
  final String venueId;
  final Field field;
  const FieldRemovedFromVenue(this.venueId, this.field);
}

// Old wizard step completion event (to be deprecated)
class Step2Completed extends CreateTournamentEvent {
  final List<Venue> venues;
  const Step2Completed(this.venues);
}

// Save event
class SaveTournamentRequested extends CreateTournamentEvent {
  const SaveTournamentRequested();
}

// Affiliation events
class CheckAffiliationStatus extends CreateTournamentEvent {
  const CheckAffiliationStatus();
}

class TournamentCreationOptionChanged extends CreateTournamentEvent {
  final String option;
  const TournamentCreationOptionChanged(this.option);
}

class LoadAffiliatedClubs extends CreateTournamentEvent {
  const LoadAffiliatedClubs();
}

class AffiliatedClubSelected extends CreateTournamentEvent {
  final String clubId;
  const AffiliatedClubSelected(this.clubId);
}

class TournamentFormatChanged extends CreateTournamentEvent {
  final String format;
  const TournamentFormatChanged(this.format);
}

// State classes
abstract class CreateTournamentState {
  const CreateTournamentState();
}

class CreateTournamentInitial extends CreateTournamentState {
  const CreateTournamentInitial();
}

class CreateTournamentStep1InProgress extends CreateTournamentState {
  final Tournament tournament;
  final String selectedCreationOption;
  final String? selectedAffiliatedClubId;

  const CreateTournamentStep1InProgress({
    required this.tournament,
    this.selectedCreationOption = 'independent',
    this.selectedAffiliatedClubId,
  });

  CreateTournamentStep1InProgress copyWith({
    Tournament? tournament,
    String? selectedCreationOption,
    String? selectedAffiliatedClubId,
  }) {
    return CreateTournamentStep1InProgress(
      tournament: tournament ?? this.tournament,
      selectedCreationOption: selectedCreationOption ?? this.selectedCreationOption,
      selectedAffiliatedClubId: selectedAffiliatedClubId ?? this.selectedAffiliatedClubId,
    );
  }
}

class CreateTournamentVenueFieldSelectionStep extends CreateTournamentState {
  final Tournament tournament;
  final String? selectedAffiliatedClubId;
  final List<Venue>? availableVenues;
  final bool venuesLoading;
  final Set<String> selectedVenueIds;
  final Map<String, List<Field>>? availableFieldsByVenueId;
  final Map<String, bool> fieldsLoadingByVenueId;
  final Map<String, Set<String>> selectedFieldIds;
  final List<FieldType>? allFieldTypes;

  const CreateTournamentVenueFieldSelectionStep({
    required this.tournament,
    this.selectedAffiliatedClubId,
    this.availableVenues,
    this.venuesLoading = false,
    this.selectedVenueIds = const {},
    this.availableFieldsByVenueId,
    this.fieldsLoadingByVenueId = const {},
    this.selectedFieldIds = const {},
    this.allFieldTypes,
  });

  CreateTournamentVenueFieldSelectionStep copyWith({
    Tournament? tournament,
    String? selectedAffiliatedClubId,
    List<Venue>? availableVenues,
    bool? venuesLoading,
    Set<String>? selectedVenueIds,
    Map<String, List<Field>>? availableFieldsByVenueId,
    Map<String, bool>? fieldsLoadingByVenueId,
    Map<String, Set<String>>? selectedFieldIds,
    List<FieldType>? allFieldTypes,
  }) {
    return CreateTournamentVenueFieldSelectionStep(
      tournament: tournament ?? this.tournament,
      selectedAffiliatedClubId: selectedAffiliatedClubId ?? this.selectedAffiliatedClubId,
      availableVenues: availableVenues ?? this.availableVenues,
      venuesLoading: venuesLoading ?? this.venuesLoading,
      selectedVenueIds: selectedVenueIds ?? this.selectedVenueIds,
      availableFieldsByVenueId: availableFieldsByVenueId ?? this.availableFieldsByVenueId,
      fieldsLoadingByVenueId: fieldsLoadingByVenueId ?? this.fieldsLoadingByVenueId,
      selectedFieldIds: selectedFieldIds ?? this.selectedFieldIds,
      allFieldTypes: allFieldTypes ?? this.allFieldTypes,
    );
  }
}

class CreateTournamentGameTimingConfigStep extends CreateTournamentState {
  final Tournament tournament;
  final String? selectedAffiliatedClubId;
  final List<Venue>? availableVenues;
  final bool venuesLoading;
  final Set<String> selectedVenueIds;
  final Map<String, List<Field>>? availableFieldsByVenueId;
  final Map<String, bool> fieldsLoadingByVenueId;
  final Map<String, Set<String>> selectedFieldIds;
  final List<FieldType>? allFieldTypes;
  final List<String> derivedAgeGroups;
  final List<String> derivedFieldSizes;

  const CreateTournamentGameTimingConfigStep({
    required this.tournament,
    this.selectedAffiliatedClubId,
    this.availableVenues,
    this.venuesLoading = false,
    this.selectedVenueIds = const {},
    this.availableFieldsByVenueId,
    this.fieldsLoadingByVenueId = const {},
    this.selectedFieldIds = const {},
    this.allFieldTypes,
    required this.derivedAgeGroups,
    required this.derivedFieldSizes,
  });

  CreateTournamentGameTimingConfigStep copyWith({
    Tournament? tournament,
    String? selectedAffiliatedClubId,
    List<Venue>? availableVenues,
    bool? venuesLoading,
    Set<String>? selectedVenueIds,
    Map<String, List<Field>>? availableFieldsByVenueId,
    Map<String, bool>? fieldsLoadingByVenueId,
    Map<String, Set<String>>? selectedFieldIds,
    List<FieldType>? allFieldTypes,
    List<String>? derivedAgeGroups,
    List<String>? derivedFieldSizes,
  }) {
    return CreateTournamentGameTimingConfigStep(
      tournament: tournament ?? this.tournament,
      selectedAffiliatedClubId: selectedAffiliatedClubId ?? this.selectedAffiliatedClubId,
      availableVenues: availableVenues ?? this.availableVenues,
      venuesLoading: venuesLoading ?? this.venuesLoading,
      selectedVenueIds: selectedVenueIds ?? this.selectedVenueIds,
      availableFieldsByVenueId: availableFieldsByVenueId ?? this.availableFieldsByVenueId,
      fieldsLoadingByVenueId: fieldsLoadingByVenueId ?? this.fieldsLoadingByVenueId,
      selectedFieldIds: selectedFieldIds ?? this.selectedFieldIds,
      allFieldTypes: allFieldTypes ?? this.allFieldTypes,
      derivedAgeGroups: derivedAgeGroups ?? this.derivedAgeGroups,
      derivedFieldSizes: derivedFieldSizes ?? this.derivedFieldSizes,
    );
  }
}

class CreateTournamentAdditionalInfoStep extends CreateTournamentState {
  final Tournament tournament;
  final String? selectedAffiliatedClubId;
  final List<Venue>? availableVenues;
  final bool venuesLoading;
  final Set<String> selectedVenueIds;
  final Map<String, List<Field>>? availableFieldsByVenueId;
  final Map<String, bool> fieldsLoadingByVenueId;
  final Map<String, Set<String>> selectedFieldIds;
  final List<FieldType>? allFieldTypes;
  final List<String> derivedAgeGroups;
  final List<String> derivedFieldSizes;

  const CreateTournamentAdditionalInfoStep({
    required this.tournament,
    this.selectedAffiliatedClubId,
    this.availableVenues,
    this.venuesLoading = false,
    this.selectedVenueIds = const {},
    this.availableFieldsByVenueId,
    this.fieldsLoadingByVenueId = const {},
    this.selectedFieldIds = const {},
    this.allFieldTypes,
    required this.derivedAgeGroups,
    required this.derivedFieldSizes,
  });

  CreateTournamentAdditionalInfoStep copyWith({
    Tournament? tournament,
    String? selectedAffiliatedClubId,
    List<Venue>? availableVenues,
    bool? venuesLoading,
    Set<String>? selectedVenueIds,
    Map<String, List<Field>>? availableFieldsByVenueId,
    Map<String, bool>? fieldsLoadingByVenueId,
    Map<String, Set<String>>? selectedFieldIds,
    List<FieldType>? allFieldTypes,
    List<String>? derivedAgeGroups,
    List<String>? derivedFieldSizes,
  }) {
    return CreateTournamentAdditionalInfoStep(
      tournament: tournament ?? this.tournament,
      selectedAffiliatedClubId: selectedAffiliatedClubId ?? this.selectedAffiliatedClubId,
      availableVenues: availableVenues ?? this.availableVenues,
      venuesLoading: venuesLoading ?? this.venuesLoading,
      selectedVenueIds: selectedVenueIds ?? this.selectedVenueIds,
      availableFieldsByVenueId: availableFieldsByVenueId ?? this.availableFieldsByVenueId,
      fieldsLoadingByVenueId: fieldsLoadingByVenueId ?? this.fieldsLoadingByVenueId,
      selectedFieldIds: selectedFieldIds ?? this.selectedFieldIds,
      allFieldTypes: allFieldTypes ?? this.allFieldTypes,
      derivedAgeGroups: derivedAgeGroups ?? this.derivedAgeGroups,
      derivedFieldSizes: derivedFieldSizes ?? this.derivedFieldSizes,
    );
  }
}

class CreateTournamentReviewStep extends CreateTournamentState {
  final Tournament tournament;
  final String? selectedAffiliatedClubId;
  final List<Venue>? availableVenues;
  final bool venuesLoading;
  final Set<String> selectedVenueIds;
  final Map<String, List<Field>>? availableFieldsByVenueId;
  final Map<String, bool> fieldsLoadingByVenueId;
  final Map<String, Set<String>> selectedFieldIds;
  final List<FieldType>? allFieldTypes;
  final List<String> derivedAgeGroups;
  final List<String> derivedFieldSizes;

  const CreateTournamentReviewStep({
    required this.tournament,
    this.selectedAffiliatedClubId,
    this.availableVenues,
    this.venuesLoading = false,
    this.selectedVenueIds = const {},
    this.availableFieldsByVenueId,
    this.fieldsLoadingByVenueId = const {},
    this.selectedFieldIds = const {},
    this.allFieldTypes,
    required this.derivedAgeGroups,
    required this.derivedFieldSizes,
  });

  CreateTournamentReviewStep copyWith({
    Tournament? tournament,
    String? selectedAffiliatedClubId,
    List<Venue>? availableVenues,
    bool? venuesLoading,
    Set<String>? selectedVenueIds,
    Map<String, List<Field>>? availableFieldsByVenueId,
    Map<String, bool>? fieldsLoadingByVenueId,
    Map<String, Set<String>>? selectedFieldIds,
    List<FieldType>? allFieldTypes,
    List<String>? derivedAgeGroups,
    List<String>? derivedFieldSizes,
  }) {
    return CreateTournamentReviewStep(
      tournament: tournament ?? this.tournament,
      selectedAffiliatedClubId: selectedAffiliatedClubId ?? this.selectedAffiliatedClubId,
      availableVenues: availableVenues ?? this.availableVenues,
      venuesLoading: venuesLoading ?? this.venuesLoading,
      selectedVenueIds: selectedVenueIds ?? this.selectedVenueIds,
      availableFieldsByVenueId: availableFieldsByVenueId ?? this.availableFieldsByVenueId,
      fieldsLoadingByVenueId: fieldsLoadingByVenueId ?? this.fieldsLoadingByVenueId,
      selectedFieldIds: selectedFieldIds ?? this.selectedFieldIds,
      allFieldTypes: allFieldTypes ?? this.allFieldTypes,
      derivedAgeGroups: derivedAgeGroups ?? this.derivedAgeGroups,
      derivedFieldSizes: derivedFieldSizes ?? this.derivedFieldSizes,
    );
  }
}

class CreateTournamentSaving extends CreateTournamentState {
  const CreateTournamentSaving();
}

class CreateTournamentSaveSuccess extends CreateTournamentState {
  const CreateTournamentSaveSuccess();
}

class CreateTournamentSaveFailure extends CreateTournamentState {
  final String error;
  const CreateTournamentSaveFailure(this.error);
}

class CreateTournamentBloc
    extends Bloc<CreateTournamentEvent, CreateTournamentState> {
  final TournamentApiService _tournamentApiService;
  final _log = Logger('CreateTournamentBloc');
  final AuthService? _authService;

  CreateTournamentBloc({
    required TournamentApiService tournamentApiService,
    AuthService? authService,
  })  : _tournamentApiService = tournamentApiService,
        _authService = authService,
        super(CreateTournamentInitial()) {
    on<InitializeTournamentCreation>(_onInitializeTournamentCreation);
    on<TournamentNameChanged>(_onTournamentNameChanged);
    on<SportTypeChanged>(_onSportTypeChanged);
    on<StartDateChanged>(_onStartDateChanged);
    on<EndDateChanged>(_onEndDateChanged);
    on<Step1Completed>(_onStep1Completed);

    // Venue/Field selection flow events
    on<LoadAvailableVenues>(_onLoadAvailableVenues);
    on<ToggleVenueSelection>(_onToggleVenueSelection);
    on<LoadFieldTypes>(_onLoadFieldTypes);
    on<LoadFieldsForVenue>(_onLoadFieldsForVenue);
    on<ToggleFieldSelection>(_onToggleFieldSelection);
    on<ProceedToNextStepFromVenueFieldSelection>(
        _onProceedToNextStepFromVenueFieldSelection); // Will go to GameTimingConfigStep

    // Game Timing Configuration event
    on<GameTimingsSubmitted>(_onGameTimingsSubmitted); // << NEW HANDLER

    // Additional Info events (keep existing ones for fields other than global game duration)
    on<AdditionalInfoStepCompleted>(_onAdditionalInfoStepCompleted); // Will go to ReviewStep
    on<FacebookUrlChanged>(_onFacebookUrlChanged);
    on<TwitterUrlChanged>(_onTwitterUrlChanged);
    on<InstagramUrlChanged>(_onInstagramUrlChanged);
    on<WebsiteUrlChanged>(_onWebsiteUrlChanged);
    on<TournamentDescriptionChanged>(_onTournamentDescriptionChanged);
    on<EarlyBirdDeadlineChanged>(_onEarlyBirdDeadlineChanged);
    on<EarlyBirdFeeChanged>(_onEarlyBirdFeeChanged);
    on<LateFeeChanged>(_onLateFeeChanged);
    on<LateRegistrationStartChanged>(_onLateRegistrationStartChanged);
    on<MaxTeamsChanged>(_onMaxTeamsChanged);
    // REMOVED: on<GameDurationChanged>(); // Global game duration event is removed
    on<MinRosterSizeChanged>(_onMinRosterSizeChanged);
    on<MaxRosterSizeChanged>(_onMaxRosterSizeChanged);
    on<AwardsChanged>(_onAwardsChanged);
    on<HasConcessionsChanged>(_onHasConcessionsChanged);
    on<HasMerchandiseChanged>(_onHasMerchandiseChanged);
    on<HasMedicalChanged>(_onHasMedicalChanged);
    on<AdmissionFeeChanged>(_onAdmissionFeeChanged);
    on<ParkingInfoChanged>(_onParkingInfoChanged);
    on<SpectatorInfoChanged>(_onSpectatorInfoChanged);
    on<SecondaryContactNameChanged>(_onSecondaryContactNameChanged);
    on<SecondaryContactEmailChanged>(_onSecondaryContactEmailChanged);
    on<SecondaryContactPhoneChanged>(_onSecondaryContactPhoneChanged);
    on<SecondaryContactRoleChanged>(_onSecondaryContactRoleChanged);

    // Save event (likely triggered from Review Screen)
    on<SaveTournamentRequested>(_onSaveTournamentRequested);

    // Affiliation events (keep as they are)
    on<CheckAffiliationStatus>(_onCheckAffiliationStatus);
    on<TournamentCreationOptionChanged>(_onTournamentCreationOptionChanged);
    on<LoadAffiliatedClubs>(_onLoadAffiliatedClubs);
    on<AffiliatedClubSelected>(_onAffiliatedClubSelected);
    on<TournamentFormatChanged>(_onTournamentFormatChanged);


    // Old Step 2 & 3 event handlers (review if these are still needed or if the new flow replaces them)
    // For now, assuming the new VenueFieldSelectionScreen replaces the old Step 2 and Step 3 concept.
    // If VenueAdded, FieldAddedToVenue etc. are still used by forms *outside* this main wizard, keep them.
    // If they were part of the old wizard steps, they might be deprecated by the new flow.
    // I'll keep them for now, assuming they might be used by standalone venue/field management.
    on<VenueAdded>(_onVenueAdded);
    on<VenueUpdated>(_onVenueUpdated);
    on<VenueRemoved>(_onVenueRemoved);
    on<Step2Completed>(_onStep2Completed); // This likely needs to be re-evaluated or removed if new flow is complete
    on<FieldAddedToVenue>(_onFieldAddedToVenue);
    on<FieldUpdatedInVenue>(_onFieldUpdatedInVenue);
    on<FieldRemovedFromVenue>(_onFieldRemovedFromVenue);

    // This event might be redundant now if ProceedToNextStepFromVenueFieldSelection handles it.
    // on<ProceedToAdditionalInfoStep>(_onProceedToAdditionalInfoStep); // Review this one
  }

  void _onTournamentFormatChanged(
    TournamentFormatChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentStep1InProgress) {
      final currentState = state as CreateTournamentStep1InProgress;
      emit(currentState.copyWith(
        tournament: currentState.tournament.copyWith(tournamentFormat: event.format),
      ));
    }
  }

  void _onInitializeTournamentCreation(
    InitializeTournamentCreation event,
    Emitter<CreateTournamentState> emit,
  ) {
    emit(CreateTournamentStep1InProgress(
      tournament: Tournament(
        name: '',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 1)),
        sportType: 'Soccer', // Default sport type
        status: 'planning',
      ),
      selectedCreationOption: 'independent', // Default
    ));
    add(CheckAffiliationStatus()); // Check affiliation right away
  }

  void _onTournamentNameChanged(
    TournamentNameChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    // This event can theoretically be dispatched from any step if the user edits the tournament name
    // from a Review screen, so _getTournamentFromState is appropriate.
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      // Find the current state to emit its copyWith
      if (state is CreateTournamentStep1InProgress) {
        emit((state as CreateTournamentStep1InProgress).copyWith(tournament: currentTournament.copyWith(name: event.name)));
      } else if (state is CreateTournamentVenueFieldSelectionStep) {
        emit((state as CreateTournamentVenueFieldSelectionStep).copyWith(tournament: currentTournament.copyWith(name: event.name)));
      } else if (state is CreateTournamentGameTimingConfigStep) {
        emit((state as CreateTournamentGameTimingConfigStep).copyWith(tournament: currentTournament.copyWith(name: event.name)));
      } else if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(name: event.name)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(name: event.name)));
      }
    }
  }

  void _onSportTypeChanged(
    SportTypeChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentStep1InProgress) {
        emit((state as CreateTournamentStep1InProgress).copyWith(tournament: currentTournament.copyWith(sportType: event.sportType)));
      }
    }
  }

  void _onStartDateChanged(
    StartDateChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentStep1InProgress) {
        emit((state as CreateTournamentStep1InProgress).copyWith(tournament: currentTournament.copyWith(startDate: event.startDate)));
      }
    }
  }

  void _onEndDateChanged(
    EndDateChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentStep1InProgress) {
        emit((state as CreateTournamentStep1InProgress).copyWith(tournament: currentTournament.copyWith(endDate: event.endDate)));
      }
    }
  }

  void _onStep1Completed(
    Step1Completed event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentStep1InProgress) {
      final currentState = state as CreateTournamentStep1InProgress;
      emit(CreateTournamentVenueFieldSelectionStep(
        tournament: event.tournamentDetails, // This now contains ageGroups string
        selectedAffiliatedClubId: currentState.selectedAffiliatedClubId,
        venuesLoading: true, // Start loading venues
      ));
      add(LoadFieldTypes()); // Trigger loading field types first, then venues
    }
  }

  void _onVenueAdded(
    VenueAdded event,
    Emitter<CreateTournamentState> emit,
  ) {
    // This handler's state usage suggests it's for a standalone venue management.
    // If it's part of the wizard flow, ensure it updates the correct wizard state (e.g., VenueFieldSelectionStep).
    if (state is CreateTournamentStep2InProgress) { // Old wizard step, might be deprecated
      final currentState = state as CreateTournamentStep2InProgress;
      final updatedVenues = List<Venue>.from(currentState.venues)..add(event.venue);
      emit(currentState.copyWith(venues: updatedVenues));
    } else if (state is CreateTournamentVenueFieldSelectionStep) { // More likely place for this event in new flow
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedVenues = List<Venue>.from(currentState.availableVenues ?? [])..add(event.venue);
      emit(currentState.copyWith(availableVenues: updatedVenues));
    }
  }

  void _onVenueUpdated(
    VenueUpdated event,
    Emitter<CreateTournamentState> emit,
  ) {
    // This handler's state usage suggests it's for a standalone venue management.
    // If it's part of the wizard flow, ensure it updates the correct wizard state (e.g., VenueFieldSelectionStep).
    if (state is CreateTournamentStep2InProgress) { // Old wizard step, might be deprecated
      final currentState = state as CreateTournamentStep2InProgress;
      final updatedVenues = currentState.venues.map((v) {
        return v.id == event.venue.id ? event.venue : v;
      }).toList();
      emit(currentState.copyWith(venues: updatedVenues));
    } else if (state is CreateTournamentVenueFieldSelectionStep) { // More likely place for this event in new flow
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedVenues = (currentState.availableVenues ?? []).map((v) {
        return v.id == event.venue.id ? event.venue : v;
      }).toList();
      emit(currentState.copyWith(availableVenues: updatedVenues));
    }
  }

  void _onVenueRemoved(
    VenueRemoved event,
    Emitter<CreateTournamentState> emit,
  ) {
    // This handler's state usage suggests it's for a standalone venue management.
    // If it's part of the wizard flow, ensure it updates the correct wizard state (e.g., VenueFieldSelectionStep).
    if (state is CreateTournamentStep2InProgress) { // Old wizard step, might be deprecated
      final currentState = state as CreateTournamentStep2InProgress;
      final updatedVenues =
          currentState.venues.where((v) => v.id != event.venue.id).toList();
      emit(currentState.copyWith(venues: updatedVenues));
    } else if (state is CreateTournamentVenueFieldSelectionStep) { // More likely place for this event in new flow
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedVenues = (currentState.availableVenues ?? [])
          .where((v) => v.id != event.venue.id)
          .toList();
      emit(currentState.copyWith(availableVenues: updatedVenues));
    }
  }

   void _onStep2Completed( // This event/handler is part of the old wizard flow and should be deprecated or removed
    Step2Completed event,
    Emitter<CreateTournamentState> emit,
  ) {
    _log.warning("Step2Completed event handler was called. This is part of an old flow. Consider deprecating/removing.");
    // This handler's logic seems to directly lead to CreateTournamentStep3InProgress, bypassing VenueFieldSelection.
    // If your app strictly follows the new wizard flow, this handler might be dead code or cause unexpected transitions.
    // Keeping it for now but noting its potential irrelevance in the new flow.
    if (state is CreateTournamentStep2InProgress) {
         final currentStep2State = state as CreateTournamentStep2InProgress;
        emit(CreateTournamentStep3InProgress(
            tournament: currentStep2State.tournament,
            venues: event.venues, // This should be `selectedVenues` from the new flow
            fieldsByVenueId: const {},
            finalSelectedAffiliatedClubId: currentStep2State.selectedAffiliatedClubId,
            finalSelectedVenueIds: currentStep2State.selectedVenueIdsFromSelection,
            finalSelectedFieldIds: currentStep2State.selectedFieldIdsFromSelection,
        ));
    } else if (state is CreateTournamentStep1InProgress) {
        final currentStep1State = state as CreateTournamentStep1InProgress;
        emit(CreateTournamentStep3InProgress(
            tournament: currentStep1State.tournament,
            venues: event.venues,
            fieldsByVenueId: const {},
        ));
    }
  }

  void _onFieldAddedToVenue(
    FieldAddedToVenue event,
    Emitter<CreateTournamentState> emit,
  ) {
    // This event likely comes from FieldFormScreen when adding a field.
    // It should update the available fields in the CreateTournamentVenueFieldSelectionStep state.
    if (state is CreateTournamentVenueFieldSelectionStep) {
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.availableFieldsByVenueId);
      final venueFields = List<Field>.from(updatedFieldsByVenueId[event.venueId] ?? []);
      venueFields.add(event.field);
      updatedFieldsByVenueId[event.venueId] = venueFields;
      emit(currentState.copyWith(availableFieldsByVenueId: updatedFieldsByVenueId));
    }
    // else if (state is CreateTournamentStep3InProgress) { // Old wizard step, might be deprecated
    //   final currentState = state as CreateTournamentStep3InProgress;
    //   final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.fieldsByVenueId);
    //   final venueFields = List<Field>.from(updatedFieldsByVenueId[event.venueId] ?? []);
    //   venueFields.add(event.field);
    //   updatedFieldsByVenueId[event.venueId] = venueFields;
    //   emit(currentState.copyWith(fieldsByVenueId: updatedFieldsByVenueId));
    // }
  }

  void _onFieldUpdatedInVenue(
    FieldUpdatedInVenue event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentVenueFieldSelectionStep) {
        final currentState = state as CreateTournamentVenueFieldSelectionStep;
        final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.availableFieldsByVenueId);
        final venueFields = updatedFieldsByVenueId[event.venueId]?.map((f) {
                return f.id == event.field.id ? event.field : f;
            }).toList() ?? [];
        updatedFieldsByVenueId[event.venueId] = venueFields;
        emit(currentState.copyWith(availableFieldsByVenueId: updatedFieldsByVenueId));
    }
    // else if (state is CreateTournamentStep3InProgress) { // Old wizard step, might be deprecated
    //   final currentState = state as CreateTournamentStep3InProgress;
    //   final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.fieldsByVenueId);
    //   final venueFields = updatedFieldsByVenueId[event.venueId]?.map((f) {
    //             return f.id == event.field.id ? event.field : f;
    //         }).toList() ?? [];
    //   updatedFieldsByVenueId[event.venueId] = venueFields;
    //   emit(currentState.copyWith(fieldsByVenueId: updatedFieldsByVenueId));
    // }
  }

  void _onFieldRemovedFromVenue(
    FieldRemovedFromVenue event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentVenueFieldSelectionStep) {
        final currentState = state as CreateTournamentVenueFieldSelectionStep;
        final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.availableFieldsByVenueId);
        final venueFields = updatedFieldsByVenueId[event.venueId]
                ?.where((f) => f.id != event.field.id)
                .toList() ?? [];
        updatedFieldsByVenueId[event.venueId] = venueFields;
        emit(currentState.copyWith(availableFieldsByVenueId: updatedFieldsByVenueId));
    }
    // else if (state is CreateTournamentStep3InProgress) { // Old wizard step, might be deprecated
    //   final currentState = state as CreateTournamentStep3InProgress;
    //   final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.fieldsByVenueId);
    //   final venueFields = updatedFieldsByVenueId[event.venueId]
    //           ?.where((f) => f.id != event.field.id)
    //           .toList() ?? [];
    //   updatedFieldsByVenueId[event.venueId] = venueFields;
    //   emit(currentState.copyWith(fieldsByVenueId: updatedFieldsByVenueId));
    // }
  }

  void _onProceedToNextStepFromVenueFieldSelection(
    ProceedToNextStepFromVenueFieldSelection event, // This event is good if it just signals to move on
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentVenueFieldSelectionStep) {
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      _log.info('Proceeding from Venue/Field selection to Game Timing Configuration.');

      // The GameTimingConfigurationScreen's initState will handle:
      // - Parsing currentState.tournament.ageGroups
      // - Deriving selectedFieldSizes from currentState.selectedVenueIds,
      //   currentState.availableFieldsByVenueId, and currentState.selectedFieldIds.
      // - Initializing its internal _timingConfigurations map either from
      //   currentState.tournament.gameTimingConfigurations (if user is coming back)
      //   or by generating defaults.

      emit(CreateTournamentGameTimingConfigStep(
        tournament: currentState.tournament,
        // Pass through all relevant data from VenueFieldSelectionStep
        // so that if the user navigates "Back" from GameTiming screen,
        // the VenueFieldSelectionScreen can be repopulated correctly.
        selectedAffiliatedClubId: currentState.selectedAffiliatedClubId,
        availableVenues: currentState.availableVenues,
        venuesLoading: currentState.venuesLoading, // Should be false by now
        selectedVenueIds: currentState.selectedVenueIds,
        availableFieldsByVenueId: currentState.availableFieldsByVenueId,
        fieldsLoadingByVenueId: currentState.fieldsLoadingByVenueId, // Should be mostly false
        selectedFieldIds: currentState.selectedFieldIds,
        allFieldTypes: currentState.allFieldTypes,
        // Pass the derived age groups and field sizes directly to the next state
        // to simplify initialization in GameTimingConfigurationScreen.
        derivedAgeGroups: event.selectedAgeGroups, // Directly from event
        derivedFieldSizes: event.selectedFieldSizes, // Directly from event
      ));
    } else {
      _log.warning('_onProceedToNextStepFromVenueFieldSelection called from unexpected state: ${state.runtimeType}');
    }
  }


  void _onGameTimingsSubmitted(
    GameTimingsSubmitted event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentGameTimingConfigStep) {
      final currentState = state as CreateTournamentGameTimingConfigStep;
      _log.info('Game Timings Submitted. Proceeding to Additional Info.');

      final updatedTournament = currentState.tournament.copyWith(
        gameTimingConfigurations: event.gameTimings,
      );

      emit(CreateTournamentAdditionalInfoStep(
        tournament: updatedTournament,
        // Pass through all data needed if user goes "Back" from AdditionalInfo to GameTiming
        // This helps GameTimingConfigurationScreen re-initialize correctly.
        selectedAffiliatedClubId: currentState.selectedAffiliatedClubId,
        availableVenues: currentState.availableVenues,
        venuesLoading: currentState.venuesLoading,
        selectedVenueIds: currentState.selectedVenueIds,
        availableFieldsByVenueId: currentState.availableFieldsByVenueId,
        fieldsLoadingByVenueId: currentState.fieldsLoadingByVenueId,
        selectedFieldIds: currentState.selectedFieldIds,
        allFieldTypes: currentState.allFieldTypes,
        derivedAgeGroups: currentState.derivedAgeGroups,
        derivedFieldSizes: currentState.derivedFieldSizes,
      ));
    } else {
      _log.warning('_onGameTimingsSubmitted called from unexpected state: ${state.runtimeType}');
      // Optionally, emit an error state or re-initialize if appropriate
    }
  }

  void _onAdditionalInfoStepCompleted(
    AdditionalInfoStepCompleted event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentAdditionalInfoStep) {
      final currentState = state as CreateTournamentAdditionalInfoStep;
      _log.info("Additional Info Completed. Proceeding to Review Step.");
      emit(CreateTournamentReviewStep(
        tournament: event.tournamentDetails,
        // Pass through all data for "Edit" buttons to navigate back correctly
        selectedAffiliatedClubId: currentState.selectedAffiliatedClubId,
        availableVenues: currentState.availableVenues,
        venuesLoading: currentState.venuesLoading,
        selectedVenueIds: currentState.selectedVenueIds,
        availableFieldsByVenueId: currentState.availableFieldsByVenueId,
        fieldsLoadingByVenueId: currentState.fieldsLoadingByVenueId,
        selectedFieldIds: currentState.selectedFieldIds,
        allFieldTypes: currentState.allFieldTypes,
        derivedAgeGroups: currentState.derivedAgeGroups,
        derivedFieldSizes: currentState.derivedFieldSizes,
      ));
    } else {
       _log.warning('_onAdditionalInfoStepCompleted called from unexpected state: ${state.runtimeType}');
    }
  }

  Future<void> _onSaveTournamentRequested(
    SaveTournamentRequested event,
    Emitter<CreateTournamentState> emit,
  ) async {
    // This should be called when the state is CreateTournamentReviewStep
    if (state is CreateTournamentReviewStep) {
      final currentState = state as CreateTournamentReviewStep;
      emit(CreateTournamentSaving());
      try {
        _log.info('Saving tournament from Review Step: ${currentState.tournament.name}');
        _log.info('Final Tournament Data: ${currentState.tournament.toJson()}');

        // Assuming your TournamentApiService.saveTournament now takes the full Tournament object
        // and handles persisting gameTimingConfigurations appropriately (e.g., as JSONB).
        await _tournamentApiService.saveTournament(currentState.tournament); // Assuming saveTournament now only takes Tournament object
        emit(CreateTournamentSaveSuccess());
      } catch (e) {
        _log.severe('Error saving tournament: $e');
        emit(CreateTournamentSaveFailure(e.toString()));
      }
    } else {
      _log.warning("SaveTournamentRequested called from unexpected state: ${state.runtimeType}");
      // Handle error appropriately, perhaps emit CreateTournamentInitial or an error state.
    }
  }

  Future<void> _onLoadFieldTypes(
    LoadFieldTypes event,
    Emitter<CreateTournamentState> emit,
  ) async {
    // This handler can be called from CreateTournamentStep1InProgress or CreateTournamentVenueFieldSelectionStep
    dynamic currentState;
    if (state is CreateTournamentStep1InProgress) {
      currentState = state as CreateTournamentStep1InProgress;
    } else if (state is CreateTournamentVenueFieldSelectionStep) {
      currentState = state as CreateTournamentVenueFieldSelectionStep;
    } else {
      _log.warning('_onLoadFieldTypes called in unexpected state: ${state.runtimeType}. Dispatching LoadAvailableVenues.');
      add(LoadAvailableVenues()); // Ensure venue loading still happens as a fallback
      return;
    }

    _log.info('Loading all field types.');
    try {
      final fieldTypes = await _tournamentApiService.getFieldTypes();
      _log.info('Loaded ${fieldTypes.length} field types.');

      if (currentState is CreateTournamentStep1InProgress) {
        // Transition to VenueFieldSelectionStep, carrying tournament and affiliation data
        // and setting up for venues loading.
        emit(CreateTournamentVenueFieldSelectionStep(
          tournament: currentState.tournament,
          selectedAffiliatedClubId: currentState.selectedAffiliatedClubId,
          allFieldTypes: fieldTypes,
          venuesLoading: true, // Indicate that venues are next to load
        ));
      } else if (currentState is CreateTournamentVenueFieldSelectionStep) {
        // If already in VenueFieldSelectionStep, just update field types and indicate venues still loading.
        emit(currentState.copyWith(allFieldTypes: fieldTypes, venuesLoading: true));
      }
      // Trigger loading available venues after field types are loaded
      add(LoadAvailableVenues());
    } catch (e) {
      _log.severe('Error loading field types: $e');
      // If field types fail, still attempt to load venues or handle error
      if (currentState is CreateTournamentStep1InProgress) {
        emit(CreateTournamentVenueFieldSelectionStep(
          tournament: currentState.tournament,
          selectedAffiliatedClubId: currentState.selectedAffiliatedClubId,
          venuesLoading: true, // Venues loading still needed
          // allFieldTypes will remain null or previous value on error
        ));
      } else if (currentState is CreateTournamentVenueFieldSelectionStep) {
        emit(currentState.copyWith(venuesLoading: true));
      }
      add(LoadAvailableVenues()); // Attempt to load venues anyway
    }
  }
  
  // Helper method to get tournament from current state if possible
  Tournament? _getTournamentFromState() {
    if (state is CreateTournamentStep1InProgress) {
      return (state as CreateTournamentStep1InProgress).tournament;
    } else if (state is CreateTournamentVenueFieldSelectionStep) {
      return (state as CreateTournamentVenueFieldSelectionStep).tournament;
    } else if (state is CreateTournamentGameTimingConfigStep) {
      return (state as CreateTournamentGameTimingConfigStep).tournament;
    } else if (state is CreateTournamentAdditionalInfoStep) {
      return (state as CreateTournamentAdditionalInfoStep).tournament;
    } else if (state is CreateTournamentReviewStep) {
      return (state as CreateTournamentReviewStep).tournament;
    }
    // Add other states that hold a Tournament object
    return null;
  }

  // --- Handlers for Tournament fields that can be updated from any step
  //     (e.g., from Review screen) ---
  void _onTournamentNameChanged(TournamentNameChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(name: event.name);
    _emitUpdatedState(updatedTournament);
  }
  void _onSportTypeChanged(SportTypeChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(sportType: event.sportType);
    _emitUpdatedState(updatedTournament);
  }
  void _onStartDateChanged(StartDateChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(startDate: event.startDate);
    _emitUpdatedState(updatedTournament);
  }
  void _onEndDateChanged(EndDateChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(endDate: event.endDate);
    _emitUpdatedState(updatedTournament);
  }
  void _onTournamentFormatChanged(TournamentFormatChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(tournamentFormat: event.format);
    _emitUpdatedState(updatedTournament);
  }
  void _onFacebookUrlChanged(FacebookUrlChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(facebookUrl: event.url);
    _emitUpdatedState(updatedTournament);
  }
  void _onTwitterUrlChanged(TwitterUrlChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(twitterUrl: event.url);
    _emitUpdatedState(updatedTournament);
  }
  void _onInstagramUrlChanged(InstagramUrlChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(instagramUrl: event.url);
    _emitUpdatedState(updatedTournament);
  }
  void _onWebsiteUrlChanged(WebsiteUrlChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(websiteUrl: event.url);
    _emitUpdatedState(updatedTournament);
  }
  void _onTournamentDescriptionChanged(TournamentDescriptionChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(description: event.description);
    _emitUpdatedState(updatedTournament);
  }
  void _onEarlyBirdDeadlineChanged(EarlyBirdDeadlineChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(earlyBirdDeadline: event.deadline);
    _emitUpdatedState(updatedTournament);
  }
  void _onEarlyBirdFeeChanged(EarlyBirdFeeChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(earlyBirdFee: event.fee);
    _emitUpdatedState(updatedTournament);
  }
  void _onLateFeeChanged(LateFeeChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(lateFee: event.fee);
    _emitUpdatedState(updatedTournament);
  }
  void _onLateRegistrationStartChanged(LateRegistrationStartChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(lateRegistrationStart: event.date);
    _emitUpdatedState(updatedTournament);
  }
  void _onMaxTeamsChanged(MaxTeamsChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(maxTeams: event.maxTeams);
    _emitUpdatedState(updatedTournament);
  }
  // No _onGameDurationChanged because it's removed
  void _onMinRosterSizeChanged(MinRosterSizeChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(minRosterSize: event.size);
    _emitUpdatedState(updatedTournament);
  }
  void _onMaxRosterSizeChanged(MaxRosterSizeChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(maxRosterSize: event.size);
    _emitUpdatedState(updatedTournament);
  }
  void _onAwardsChanged(AwardsChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(awards: event.awards);
    _emitUpdatedState(updatedTournament);
  }
  void _onHasConcessionsChanged(HasConcessionsChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(hasConcessions: event.value);
    _emitUpdatedState(updatedTournament);
  }
  void _onHasMerchandiseChanged(HasMerchandiseChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(hasMerchandise: event.value);
    _emitUpdatedState(updatedTournament);
  }
  void _onHasMedicalChanged(HasMedicalChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(hasMedical: event.value);
    _emitUpdatedState(updatedTournament);
  }
  void _onAdmissionFeeChanged(AdmissionFeeChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(admissionFee: event.fee);
    _emitUpdatedState(updatedTournament);
  }
  void _onParkingInfoChanged(ParkingInfoChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(parkingInfo: event.info);
    _emitUpdatedState(updatedTournament);
  }
  void _onSpectatorInfoChanged(SpectatorInfoChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(spectatorInfo: event.info);
    _emitUpdatedState(updatedTournament);
  }
  void _onSecondaryContactNameChanged(SecondaryContactNameChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(secondaryContactName: event.name);
    _emitUpdatedState(updatedTournament);
  }
  void _onSecondaryContactEmailChanged(SecondaryContactEmailChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(secondaryContactEmail: event.email);
    _emitUpdatedState(updatedTournament);
  }
  void _onSecondaryContactPhoneChanged(SecondaryContactPhoneChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(secondaryContactPhone: event.phone);
    _emitUpdatedState(updatedTournament);
  }
  void _onSecondaryContactRoleChanged(SecondaryContactRoleChanged event, Emitter<CreateTournamentState> emit) {
    final tournament = _getTournamentFromState();
    if (tournament == null) return;
    final updatedTournament = tournament.copyWith(secondaryContactRole: event.role);
    _emitUpdatedState(updatedTournament);
  }

  // Helper to emit the correct state based on the current state type
  // This avoids repeating long if/else if chains for each onChanged handler
  void _emitUpdatedState(Tournament updatedTournament) {
    if (state is CreateTournamentStep1InProgress) {
      emit((state as CreateTournamentStep1InProgress).copyWith(tournament: updatedTournament));
    } else if (state is CreateTournamentVenueFieldSelectionStep) {
      emit((state as CreateTournamentVenueFieldSelectionStep).copyWith(tournament: updatedTournament));
    } else if (state is CreateTournamentGameTimingConfigStep) {
      emit((state as CreateTournamentGameTimingConfigStep).copyWith(tournament: updatedTournament));
    } else if (state is CreateTournamentAdditionalInfoStep) {
      emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: updatedTournament));
    } else if (state is CreateTournamentReviewStep) {
      emit((state as CreateTournamentReviewStep).copyWith(tournament: updatedTournament));
    } else {
      _log.warning("Attempted to emit updated tournament from unexpected state: ${state.runtimeType}");
    }
  }


  // --- Old Step 2 & 3 handlers (review if these are truly needed with the new flow) ---
  // These are less likely to be correct in the new flow and might represent an older design.
  // The primary flow should be Step1 -> VenueFieldSelection -> GameTiming -> AdditionalInfo -> Review -> Save.
  void _onVenueAdded(VenueAdded event, Emitter<CreateTournamentState> emit) {
    if (state is CreateTournamentStep2InProgress) { // Old wizard step, might be deprecated
      final currentState = state as CreateTournamentStep2InProgress;
      final updatedVenues = List<Venue>.from(currentState.venues)..add(event.venue);
      emit(currentState.copyWith(venues: updatedVenues));
    } else if (state is CreateTournamentVenueFieldSelectionStep) { // More likely place for this event in new flow
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedVenues = List<Venue>.from(currentState.availableVenues ?? [])..add(event.venue);
      emit(currentState.copyWith(availableVenues: updatedVenues));
    }
  }

  void _onVenueUpdated(VenueUpdated event, Emitter<CreateTournamentState> emit) {
    if (state is CreateTournamentStep2InProgress) { // Old wizard step, might be deprecated
      final currentState = state as CreateTournamentStep2InProgress;
      final updatedVenues = currentState.venues.map((v) {
        return v.id == event.venue.id ? event.venue : v;
      }).toList();
      emit(currentState.copyWith(venues: updatedVenues));
    } else if (state is CreateTournamentVenueFieldSelectionStep) { // More likely place for this event in new flow
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedVenues = (currentState.availableVenues ?? []).map((v) {
        return v.id == event.venue.id ? event.venue : v;
      }).toList();
      emit(currentState.copyWith(availableVenues: updatedVenues));
    }
  }

  void _onVenueRemoved(VenueRemoved event, Emitter<CreateTournamentState> emit) {
    if (state is CreateTournamentStep2InProgress) { // Old wizard step, might be deprecated
      final currentState = state as CreateTournamentStep2InProgress;
      final updatedVenues =
          currentState.venues.where((v) => v.id != event.venue.id).toList();
      emit(currentState.copyWith(venues: updatedVenues));
    } else if (state is CreateTournamentVenueFieldSelectionStep) { // More likely place for this event in new flow
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedVenues = (currentState.availableVenues ?? [])
          .where((v) => v.id != event.venue.id)
          .toList();
      emit(currentState.copyWith(availableVenues: updatedVenues));
    }
  }

   void _onStep2Completed( // This event/handler is part of the old wizard flow and should be deprecated or removed
    Step2Completed event,
    Emitter<CreateTournamentState> emit,
  ) {
    _log.warning("Step2Completed event handler was called. This is part of an old flow. Consider deprecating/removing.");
    // This handler's logic seems to directly lead to CreateTournamentStep3InProgress, bypassing VenueFieldSelection.
    // If your app strictly follows the new wizard flow, this handler might be dead code or cause unexpected transitions.
    // Keeping it for now but noting its potential irrelevance in the new flow.
    if (state is CreateTournamentStep2InProgress) {
         final currentStep2State = state as CreateTournamentStep2InProgress;
        emit(CreateTournamentStep3InProgress(
            tournament: currentStep2State.tournament,
            venues: event.venues,
            fieldsByVenueId: const {},
            finalSelectedAffiliatedClubId: currentStep2State.selectedAffiliatedClubId,
            finalSelectedVenueIds: currentStep2State.selectedVenueIdsFromSelection,
            finalSelectedFieldIds: currentStep2State.selectedFieldIdsFromSelection,
        ));
    } else if (state is CreateTournamentStep1InProgress) {
        final currentStep1State = state as CreateTournamentStep1InProgress;
        emit(CreateTournamentStep3InProgress(
            tournament: currentStep1State.tournament,
            venues: event.venues,
            fieldsByVenueId: const {},
        ));
    }
  }

  void _onFieldAddedToVenue(FieldAddedToVenue event, Emitter<CreateTournamentState> emit) {
    if (state is CreateTournamentVenueFieldSelectionStep) {
        final currentState = state as CreateTournamentVenueFieldSelectionStep;
        final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.availableFieldsByVenueId);
        final venueFields = List<Field>.from(updatedFieldsByVenueId[event.venueId] ?? []);
        venueFields.add(event.field);
        updatedFieldsByVenueId[event.venueId] = venueFields;
        emit(currentState.copyWith(availableFieldsByVenueId: updatedFieldsByVenueId));
    }
  }

  void _onFieldUpdatedInVenue(FieldUpdatedInVenue event, Emitter<CreateTournamentState> emit) {
    if (state is CreateTournamentVenueFieldSelectionStep) {
        final currentState = state as CreateTournamentVenueFieldSelectionStep;
        final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.availableFieldsByVenueId);
        final venueFields = updatedFieldsByVenueId[event.venueId]?.map((f) {
                return f.id == event.field.id ? event.field : f;
            }).toList() ?? [];
        updatedFieldsByVenueId[event.venueId] = venueFields;
        emit(currentState.copyWith(availableFieldsByVenueId: updatedFieldsByVenueId));
    }
  }

  void _onFieldRemovedFromVenue(FieldRemovedFromVenue event, Emitter<CreateTournamentState> emit) {
    if (state is CreateTournamentVenueFieldSelectionStep) {
        final currentState = state as CreateTournamentVenueFieldSelectionStep;
        final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.availableFieldsByVenueId);
        final venueFields = updatedFieldsByVenueId[event.venueId]
                ?.where((f) => f.id != event.field.id)
                .toList() ?? [];
        updatedFieldsByVenueId[event.venueId] = venueFields;
        emit(currentState.copyWith(availableFieldsByVenueId: updatedFieldsByVenueId));
    }
  }

  // No _onProceedToAdditionalInfoStep if it's now handled by _onGameTimingsSubmitted directly.

}
