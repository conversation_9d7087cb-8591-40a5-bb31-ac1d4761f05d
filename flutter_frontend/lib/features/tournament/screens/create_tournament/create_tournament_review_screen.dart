// soccer_frontend/features/tournament/presentation/screens/create_tournament_review_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart'; // Import GoRouter
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:soccer_frontend/data/models/venue.dart'; // Import Venue model
import 'package:soccer_frontend/data/models/field.dart'; // Import Field model
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart'; // Import AuthBloc for logout
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_state.dart';
import 'package:intl/intl.dart'; // For date formatting

class CreateTournamentReviewScreen extends StatelessWidget {
  const CreateTournamentReviewScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // We expect the state to be CreateTournamentReviewStep when this screen is active.
    // BlocConsumer is used here to react to saving states (loading, success, failure)
    // while BlocBuilder is used for the primary display.
    return Scaffold(
      appBar: AppBar(
        title: const Text('Review Tournament Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              context.read<AuthBloc>().add(AuthSignOutRequested());
            },
            tooltip: 'Sign Out',
          ),
        ],
      ),
      body: BlocConsumer<CreateTournamentBloc, CreateTournamentState>(
        listener: (context, state) {
          if (state is CreateTournamentSaveSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Tournament saved successfully!')),
            );
            context.go('/director-dashboard'); // Navigate to dashboard after save
          } else if (state is CreateTournamentSaveFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Failed to save tournament: ${state.error}')),
            );
          }
        },
        // Only rebuild the UI when the state is the review step or saving states change.
        buildWhen: (previous, current) =>
            current is CreateTournamentReviewStep ||
            current is CreateTournamentSaving ||
            current is CreateTournamentSaveFailure,
        builder: (context, blocState) {
          final isSaving = blocState is CreateTournamentSaving;
          final Tournament tournament;
          // Cast the state to CreateTournamentReviewStep for data access.
          // This should be the expected state.
          if (blocState is CreateTournamentReviewStep) {
            tournament = blocState.tournament;
          } else if (blocState is CreateTournamentSaving || blocState is CreateTournamentSaveFailure) {
            // If we are in a saving state, we need to get the tournament from the previous review state
            // to display it. This is a common pattern.
            final previousReviewState = context.read<CreateTournamentBloc>().state;
            if (previousReviewState is CreateTournamentReviewStep) {
              tournament = previousReviewState.tournament;
            } else {
              // Fallback for unexpected state during saving. Should ideally not happen.
              return const Center(child: CircularProgressIndicator());
            }
          }
          else {
            return const Center(child: CircularProgressIndicator()); // Should not happen in a correct flow
          }


          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader(context, 'Basic Tournament Information', () {
                  context.go('/create-tournament/step1'); // Navigate back to Step 1
                }),
                _buildDetailItem(context, 'Name', tournament.name),
                _buildDetailItem(context, 'Format', tournament.tournamentFormat ?? 'N/A'),
                _buildDetailItem(context, 'City', tournament.city ?? 'N/A'),
                _buildDetailItem(context, 'State', tournament.state ?? 'N/A'),
                _buildDetailItem(context, 'Start Date', DateFormat.yMMMd().format(tournament.startDate.toLocal())),
                _buildDetailItem(context, 'End Date', DateFormat.yMMMd().format(tournament.endDate.toLocal())),
                _buildDetailItem(context, 'Reg. Deadline', tournament.registrationDeadline != null ? DateFormat.yMMMd().format(tournament.registrationDeadline!.toLocal()) : 'N/A'),
                _buildDetailItem(context, 'Age Groups', tournament.ageGroups ?? 'N/A'),
                _buildDetailItem(context, 'Games Per Team', tournament.gamesPerTeam?.toString() ?? 'N/A'),
                _buildDetailItem(context, 'Managed By', tournament.managingClubId != null ? 'Affiliated Club' : 'Independent Director'),
                const SizedBox(height: 24),

                _buildSectionHeader(context, 'Venues & Fields', () {
                  context.go('/create-tournament/venue-field-selection'); // Navigate back to Venue/Field selection
                }),
                if (blocState is CreateTournamentReviewStep && blocState.selectedVenueIds.isEmpty) // Access selectedVenueIds from blocState
                  const Text('No venues or fields selected.', style: TextStyle(fontStyle: FontStyle.italic))
                else if (blocState is CreateTournamentReviewStep) ...[
                  Text('Selected Venues (${blocState.selectedVenueIds.length}):', style: Theme.of(context).textTheme.titleSmall),
                  ...blocState.selectedVenueIds.map((venueId) {
                    final venue = blocState.availableVenues?.where((v) => v.id == venueId).firstOrNull;
                    return Padding(
                      padding: const EdgeInsets.only(left: 8.0, top: 4.0),
                      child: Text('• ${venue?.name ?? venueId}'),
                    );
                  }).toList(),
                  const SizedBox(height: 8),
                  Text('Selected Fields (${blocState.selectedFieldIds.length}):', style: Theme.of(context).textTheme.titleSmall),
                  ...blocState.selectedFieldIds.map((fieldId) {
                    final field = blocState.availableFieldsByVenueId.values
                        .expand((fields) => fields)
                        .where((f) => f.id == fieldId).firstOrNull;
                    return Padding(
                      padding: const EdgeInsets.only(left: 8.0, top: 4.0),
                      child: Text('• ${field?.nameOrNumber ?? fieldId} (${field?.size ?? 'N/A'})'),
                    );
                  }).toList(),
                ],
                const SizedBox(height: 24),

                _buildSectionHeader(context, 'Game Timing Configurations', () {
                  context.go('/create-tournament/game-timing'); // Navigate back to Game Timing Config
                }),
                if (tournament.gameTimingConfigurations?.isEmpty ?? true)
                  const Text('No game timing configurations set.', style: TextStyle(fontStyle: FontStyle.italic))
                else
                  ..._buildGameTimingConfigs(context, tournament),

                const SizedBox(height: 24),
                _buildSectionHeader(context, 'Additional Information', () {
                  context.go('/create-tournament/additional-info'); // Navigate back to Additional Info
                }),
                _buildDetailItem(context, 'Description', tournament.description?.isNotEmpty == true ? tournament.description! : 'N/A'),
                _buildDetailItem(context, 'Rules', tournament.rules?.isNotEmpty == true ? tournament.rules! : 'N/A'),
                _buildDetailItem(context, 'Refund Policy', tournament.refundPolicy?.isNotEmpty == true ? tournament.refundPolicy! : 'N/A'),
                _buildDetailItem(context, 'Early Bird Deadline', tournament.earlyBirdDeadline != null ? DateFormat.yMMMd().format(tournament.earlyBirdDeadline!.toLocal()) : 'N/A'),
                _buildDetailItem(context, 'Early Bird Fee', tournament.earlyBirdFee != null ? '\$${tournament.earlyBirdFee!.toStringAsFixed(2)}' : 'N/A'),
                _buildDetailItem(context, 'Late Reg. Start', tournament.lateRegistrationStart != null ? DateFormat.yMMMd().format(tournament.lateRegistrationStart!.toLocal()) : 'N/A'),
                _buildDetailItem(context, 'Late Fee', tournament.lateFee != null ? '\$${tournament.lateFee!.toStringAsFixed(2)}' : 'N/A'),
                _buildDetailItem(context, 'Max Teams', tournament.maxTeams?.toString() ?? 'N/A'),
                _buildDetailItem(context, 'Min Roster Size', tournament.minRosterSize?.toString() ?? 'N/A'),
                _buildDetailItem(context, 'Max Roster Size', tournament.maxRosterSize?.toString() ?? 'N/A'),
                _buildDetailItem(context, 'Awards', tournament.awards?.isNotEmpty == true ? tournament.awards! : 'N/A'),
                _buildDetailItem(context, 'Concessions', (tournament.hasConcessions ?? false) ? 'Yes' : 'No'),
                _buildDetailItem(context, 'Merchandise', (tournament.hasMerchandise ?? false) ? 'Yes' : 'No'),
                _buildDetailItem(context, 'Medical Services', (tournament.hasMedical ?? false) ? 'Yes' : 'No'),
                _buildDetailItem(context, 'Admission Fee', tournament.admissionFee?.isNotEmpty == true ? tournament.admissionFee! : 'N/A'),
                _buildDetailItem(context, 'Parking Info', tournament.parkingInfo?.isNotEmpty == true ? tournament.parkingInfo! : 'N/A'),
                _buildDetailItem(context, 'Spectator Info', tournament.spectatorInfo?.isNotEmpty == true ? tournament.spectatorInfo! : 'N/A'),
                _buildDetailItem(context, 'Director Name', tournament.directorName ?? 'N/A'),
                _buildDetailItem(context, 'Director Email', tournament.directorEmail ?? 'N/A'),
                _buildDetailItem(context, 'Director Phone', tournament.directorPhone ?? 'N/A'),
                _buildDetailItem(context, 'Secondary Contact Name', tournament.secondaryContactName ?? 'N/A'),
                _buildDetailItem(context, 'Secondary Contact Email', tournament.secondaryContactEmail ?? 'N/A'),
                _buildDetailItem(context, 'Secondary Contact Phone', tournament.secondaryContactPhone ?? 'N/A'),
                _buildDetailItem(context, 'Secondary Contact Role', tournament.secondaryContactRole ?? 'N/A'),
                const SizedBox(height: 24),

                // Registration Fees by Field Size
                _buildSectionHeader(context, 'Registration Fees', () {
                    // Navigate back to Step 1 to edit registration fees
                    context.go('/create-tournament/step1');
                }),
                if (tournament.registrationFeesByFieldSize?.isEmpty ?? true)
                    const Text('No registration fees configured.', style: TextStyle(fontStyle: FontStyle.italic))
                else ...[
                    ...tournament.registrationFeesByFieldSize!.entries.map((entry) {
                        return _buildDetailItem(context, entry.key, '\$${entry.value.toStringAsFixed(2)}');
                    }).toList(),
                ],
                const SizedBox(height: 32),


                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    OutlinedButton(
                      onPressed: () {
                        context.go('/create-tournament/additional-info'); // Navigate back to Additional Info
                      },
                      child: const Text('Back'),
                    ),
                    ElevatedButton(
                      onPressed: isSaving ? null : () {
                        context.read<CreateTournamentBloc>().add(
                          SaveTournamentRequested(),
                        );
                      },
                      child: isSaving
                          ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(strokeWidth: 2.0, color: Colors.white))
                          : const Text('Save Tournament'),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, VoidCallback onEdit) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          TextButton.icon(
            icon: const Icon(Icons.edit, size: 18),
            label: const Text('Edit'),
            onPressed: onEdit,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(BuildContext context, String label, String value) {
    // Hide fields that are "N/A" unless explicitly specified to show (e.g., if it's a checkbox that means "no")
    // Also, handle some fields that should explicitly hide if empty/N/A
    final hideIfNA = !(label == 'Concessions' || label == 'Merchandise' || label == 'Medical Services' ||
                       label == 'Rules' || label == 'Refund Policy');

    if (hideIfNA && value == 'N/A') {
      return const SizedBox.shrink();
    }
    // Specific check for fields that are typically free text but might be "N/A" if empty
    if ((label == 'Description' || label == 'Awards' || label == 'Admission Fee' || label == 'Parking Info' || label == 'Spectator Info') && value == 'N/A') {
      return const SizedBox.shrink();
    }


    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150, // Fixed width for labels for alignment
            child: Text('$label:', style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500)),
          ),
          Expanded(child: Text(value, style: Theme.of(context).textTheme.bodyLarge)),
        ],
      ),
    );
  }


  List<Widget> _buildGameTimingConfigs(BuildContext context, Tournament tournament) {
    final sortedEntries = tournament.gameTimingConfigurations!.entries.toList()
      ..sort((a, b) {
        // Sort by ageGroup, then by fieldSize for consistent display
        final ageCompare = a.value.ageGroup.compareTo(b.value.ageGroup);
        if (ageCompare != 0) return ageCompare;
        return a.value.fieldSize.compareTo(b.value.fieldSize);
      });

    return sortedEntries.map((entry) {
      final config = entry.value;
      return Card(
        margin: const EdgeInsets.only(bottom: 12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${config.ageGroup} • ${config.fieldSize}', // Use dot for consistency
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    'Total: ${config.totalTimeSlotMinutes} min',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              _buildDetailItem(context, 'Game Duration', '${config.gameDurationMinutes} min'),
              _buildDetailItem(context, 'Halftime', '${config.halftimeMinutes} min'),
              _buildDetailItem(context, 'Buffer Time', '${config.bufferTimeMinutes} min'),
              if (config.notes.isNotEmpty) ...[ // Use .isNotEmpty for String notes
                const SizedBox(height: 8),
                Text('Notes: ${config.notes}', style: Theme.of(context).textTheme.bodySmall?.copyWith(fontStyle: FontStyle.italic)),
              ],
            ],
          ),
        ),
      );
    }).toList();
  }
}