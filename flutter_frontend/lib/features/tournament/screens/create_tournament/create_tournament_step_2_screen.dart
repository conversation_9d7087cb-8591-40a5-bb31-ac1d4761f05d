import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:soccer_frontend/data/models/field.dart';
import 'package:soccer_frontend/data/models/venue.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_state.dart';

class CreateTournamentStep2Screen extends StatefulWidget {
  const CreateTournamentStep2Screen({Key? key}) : super(key: key);

  @override
  State<CreateTournamentStep2Screen> createState() => _CreateTournamentStep2ScreenState();
}

class _CreateTournamentStep2ScreenState extends State<CreateTournamentStep2Screen> {
  @override
  void initState() {
    super.initState();
    // Load venues when the screen initializes
    context.read<CreateTournamentBloc>().add(LoadAvailableVenues());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Venues and Fields'),
      ),
      body: BlocConsumer<CreateTournamentBloc, CreateTournamentState>(
        listener: (context, state) {
          if (state is CreateTournamentStep2InProgress) {
            // Navigate to the next step (old Step 2 - Venue Management)
            context.go('/create-tournament/step3');
          }
        },
        builder: (context, state) {
          if (state is! CreateTournamentVenueFieldSelectionStep) {
            return const Center(child: CircularProgressIndicator());
          }

          final selectionState = state;

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Select Venues and Fields for ${selectionState.tournament.name}',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),

                // Venues section
                Text(
                  'Available Venues',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),

                if (selectionState.venuesLoading)
                  const Center(child: CircularProgressIndicator())
                else if (selectionState.availableVenues == null || selectionState.availableVenues!.isEmpty)
                  const Text('No venues available. Please add venues first.')
                else
                  Expanded(
                    child: ListView(
                      children: [
                        ...selectionState.availableVenues!.map((venue) {
                          final isSelected = selectionState.selectedVenueIds.contains(venue.id);
                          final isExpanded = isSelected;

                          return Card(
                            margin: const EdgeInsets.only(bottom: 8.0),
                            child: Column(
                              children: [
                                // Venue header with checkbox
                                CheckboxListTile(
                                  key: Key('venue_checkbox_${venue.id}'),
                                  title: Text(venue.name),
                                  subtitle: Text(venue.address),
                                  value: isSelected,
                                  onChanged: (bool? value) {
                                    context.read<CreateTournamentBloc>().add(
                                      ToggleVenueSelection(venue.id!)
                                    );

                                    // If venue is selected, load its fields
                                    if (value == true) {
                                      context.read<CreateTournamentBloc>().add(
                                        LoadFieldsForVenue(venue.id!)
                                      );
                                    }
                                  },
                                ),

                                // Fields section (only shown if venue is selected)
                                if (isExpanded) ...[
                                  const Divider(),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        'Fields at ${venue.name}',
                                        style: Theme.of(context).textTheme.titleSmall,
                                      ),
                                    ),
                                  ),

                                  // Show loading indicator while fields are loading
                                  if (selectionState.fieldsLoadingByVenueId[venue.id] == true)
                                    const Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: Center(child: CircularProgressIndicator()),
                                    )
                                  // Show fields if available
                                  else if (selectionState.availableFieldsByVenueId[venue.id]?.isNotEmpty == true)
                                    ...selectionState.availableFieldsByVenueId[venue.id]!.map((field) {
                                      final isFieldSelected = selectionState.selectedFieldIds.contains(field.id);

                                      return CheckboxListTile(
                                        key: Key('field_checkbox_${field.id}'),
                                        title: Text(field.nameOrNumber),
                                        subtitle: Text('Type: ${field.surfaceType ?? 'Unknown'}'), // Changed fieldTypeName to surfaceType
                                        value: isFieldSelected,
                                        onChanged: (bool? value) {
                                          context.read<CreateTournamentBloc>().add(
                                            ToggleFieldSelection(field.id!)
                                          );
                                        },
                                        dense: true,
                                        controlAffinity: ListTileControlAffinity.leading,
                                      );
                                    }).toList()
                                  // Show message if no fields available
                                  else
                                    const Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: Text('No fields available for this venue.'),
                                    ),
                                ],
                              ],
                            ),
                          );
                        }).toList(),

                        const SizedBox(height: 16),

                        // Next button
                        Align(
                          alignment: Alignment.centerRight,
                          child: ElevatedButton(
                            key: const Key('next_button'),
                            onPressed: selectionState.selectedVenueIds.isEmpty ||
                                      selectionState.selectedFieldIds.isEmpty
                                ? null // Disable if no venues or fields selected
                                : () {
                                    // Prepare selected venues and fields
                                    final selectedVenues = selectionState.availableVenues!
                                        .where((v) => selectionState.selectedVenueIds.contains(v.id))
                                        .toList();

                                    final selectedFieldsByVenueId = <String, List<Field>>{};
                                    for (final venueId in selectionState.selectedVenueIds) {
                                      final venueFields = selectionState.availableFieldsByVenueId[venueId] ?? [];
                                      final selectedFields = venueFields
                                          .where((f) => selectionState.selectedFieldIds.contains(f.id))
                                          .toList();

                                      if (selectedFields.isNotEmpty) {
                                        selectedFieldsByVenueId[venueId] = selectedFields;
                                      }
                                    }

                                    context.read<CreateTournamentBloc>().add(
                                      ProceedToNextStepFromVenueFieldSelection(
                                        selectedVenues: selectedVenues,
                                        selectedFieldsByVenueId: selectedFieldsByVenueId,
                                      ),
                                    );
                                  },
                            child: const Text('Next'),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
