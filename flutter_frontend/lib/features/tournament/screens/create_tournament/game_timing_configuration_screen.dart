import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:soccer_frontend/data/models/game_timing_config.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_state.dart';

class GameTimingConfigurationScreen extends StatefulWidget {
  const GameTimingConfigurationScreen({super.key});

  @override
  State<GameTimingConfigurationScreen> createState() => _GameTimingConfigurationScreenState();
}

class _GameTimingConfigurationScreenState extends State<GameTimingConfigurationScreen> {
  final Map<String, GameTimingConfig> _gameTimings = {};
  final Set<String> _selectedAgeGroups = {};
  final Set<String> _selectedFieldSizes = {};

  @override
  void initState() {
    super.initState();
    _parseAgeGroupsAndFieldSizes();
  }

  void _parseAgeGroupsAndFieldSizes() {
    final state = context.read<CreateTournamentBloc>().state;
    
    // Parse age groups from tournament.ageGroups
    if (state is CreateTournamentGameTimingConfigStep) {
      if (state.tournament.ageGroups != null && state.tournament.ageGroups!.isNotEmpty) {
        // Example format: "Boys U10, U12 and Girls U14"
        final rawAgeGroups = state.tournament.ageGroups!.split(',');
        for (var rawGroup in rawAgeGroups) {
          if (rawGroup.toLowerCase().contains("boys")) {
            RegExp exp = RegExp(r"U\d+|Adult", caseSensitive: false);
            exp.allMatches(rawGroup).forEach((match) => _selectedAgeGroups.add(match.group(0)!.toUpperCase()));
          } else if (rawGroup.toLowerCase().contains("girls")) {
            RegExp exp = RegExp(r"U\d+|Adult", caseSensitive: false);
            exp.allMatches(rawGroup).forEach((match) => _selectedAgeGroups.add("G-${match.group(0)!.toUpperCase()}"));
          } else {
            _selectedAgeGroups.add(rawGroup.trim().toUpperCase());
          }
        }
      }

      // Parse field sizes from selected fields
      for (final venueId in state.selectedVenueIds) {
        final fieldsForVenue = state.availableFieldsByVenueId[venueId];
        if (fieldsForVenue != null) {
          for (final field in fieldsForVenue) {
            if (state.selectedFieldIds.contains(field.id) && field.size != null) {
              _selectedFieldSizes.add(field.size!);
            }
          }
        }
      }

      // Initialize default configurations
      _initializeDefaultConfigurations();
    }
  }

  void _initializeDefaultConfigurations() {
    for (final ageGroup in _selectedAgeGroups) {
      for (final fieldSize in _selectedFieldSizes) {
        final config = GameTimingConfig.getConfigurationFor(ageGroup, fieldSize) ??
            GameTimingConfig(
              ageGroup: ageGroup,
              fieldSize: fieldSize,
              gameDurationMinutes: 60,
              halftimeMinutes: 10,
              bufferTimeMinutes: 10,
              notes: 'Generic default - please review and customize',
            );
        _gameTimings['${ageGroup}_$fieldSize'] = config;
      }
    }
  }

  void _updateTimingConfig(String key, GameTimingConfig config) {
    setState(() {
      _gameTimings[key] = config;
    });
  }

  void _submitTimings() {
    context.read<CreateTournamentBloc>().add(GameTimingsSubmitted(_gameTimings));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Game Timing Configurations'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: ListView(
                children: _gameTimings.entries.map((entry) {
                  return _TimingConfigCard(
                    config: entry.value,
                    onChanged: (newConfig) => _updateTimingConfig(entry.key, newConfig),
                  );
                }).toList(),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _submitTimings,
              child: const Text('Continue'),
            ),
          ],
        ),
      ),
    );
  }
}

class _TimingConfigCard extends StatefulWidget {
  final GameTimingConfig config;
  final Function(GameTimingConfig) onChanged;

  const _TimingConfigCard({
    required this.config,
    required this.onChanged,
  });

  @override
  State<_TimingConfigCard> createState() => _TimingConfigCardState();
}

class _TimingConfigCardState extends State<_TimingConfigCard> {
  late GameTimingConfig _currentConfig;

  @override
  void initState() {
    super.initState();
    _currentConfig = widget.config;
  }

  void _updateGameDuration(int minutes) {
    setState(() {
      _currentConfig = _currentConfig.copyWith(gameDurationMinutes: minutes);
    });
    widget.onChanged(_currentConfig);
  }

  void _updateHalftime(int minutes) {
    setState(() {
      _currentConfig = _currentConfig.copyWith(halftimeMinutes: minutes);
    });
    widget.onChanged(_currentConfig);
  }

  void _updateBufferTime(int minutes) {
    setState(() {
      _currentConfig = _currentConfig.copyWith(bufferTimeMinutes: minutes);
    });
    widget.onChanged(_currentConfig);
  }

  void _updateNotes(String notes) {
    setState(() {
      _currentConfig = _currentConfig.copyWith(notes: notes);
    });
    widget.onChanged(_currentConfig);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${_currentConfig.ageGroup} - ${_currentConfig.fieldSize}',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _currentConfig.notes,
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 16),
            _NumberInputField(
              label: 'Game Duration (minutes)',
              value: _currentConfig.gameDurationMinutes,
              onChanged: _updateGameDuration,
            ),
            _NumberInputField(
              label: 'Halftime Duration (minutes)',
              value: _currentConfig.halftimeMinutes,
              onChanged: _updateHalftime,
            ),
            _NumberInputField(
              label: 'Buffer Time (minutes)',
              value: _currentConfig.bufferTimeMinutes,
              onChanged: _updateBufferTime,
            ),
            const SizedBox(height: 8),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Notes',
                border: OutlineInputBorder(),
              ),
              onChanged: _updateNotes,
              controller: TextEditingController(text: _currentConfig.notes),
            ),
            const SizedBox(height: 8),
            Text(
              'Total Time Slot: ${_currentConfig.totalTimeSlotMinutes} minutes',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}

class _NumberInputField extends StatelessWidget {
  final String label;
  final int value;
  final Function(int) onChanged;

  const _NumberInputField({
    required this.label,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: Text(label),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: TextFormField(
              initialValue: value.toString(),
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
              ),
              onChanged: (value) {
                final parsed = int.tryParse(value);
                if (parsed != null) {
                  onChanged(parsed);
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
