import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/tournament_state.dart';
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:intl/intl.dart';

class TournamentDetailScreen extends StatefulWidget {
  final String tournamentId;

  const TournamentDetailScreen({super.key, required this.tournamentId});

  static String routeName(String tournamentId) => '/tournament/$tournamentId';

  @override
  State<TournamentDetailScreen> createState() => _TournamentDetailScreenState();
}

class _TournamentDetailScreenState extends State<TournamentDetailScreen> {
  @override
  void initState() {
    super.initState();
    context
        .read<TournamentBloc>()
        .add(TournamentLoadByIdRequested(id: widget.tournamentId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tournament Details'),
      ),
      body: BlocBuilder<TournamentBloc, TournamentState>(
        builder: (context, state) {
          if (state is TournamentLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is TournamentDetailLoaded) {
            final tournament = state.tournament;
            return _buildTournamentDetails(context, tournament);
          } else if (state is TournamentError) {
            return Center(
              child: Text('Error loading tournament details: ${state.message}'),
            );
          }
          return const Center(child: Text('Loading details...'));
        },
      ),
    );
  }

  Widget _buildTournamentDetails(BuildContext context, Tournament tournament) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ListView(
        children: <Widget>[
          _buildDetailItem(context, 'Name', tournament.name),
          _buildDetailItem(context, 'Sport', tournament.sportType),
          _buildDetailItem(context, 'Status', tournament.status),
          _buildDetailItem(context, 'Start Date', DateFormat.yMMMd().format(tournament.startDate.toLocal())),
          _buildDetailItem(context, 'End Date', DateFormat.yMMMd().format(tournament.endDate.toLocal())),
          // Add more details as needed from the Tournament model
          // e.g., location, rules_url, etc. if they were populated.
          // For now, these are the core fields from the model.
          const SizedBox(height: 20),
          // TODO: Add buttons for actions like "Manage Venues", "Manage Age Groups", "Schedule Matches" etc.
          ElevatedButton(
            onPressed: () {
              // Example action: Navigate to edit tournament (if such a screen exists)
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Edit for ${tournament.name} (TODO)')),
              );
            },
            child: const Text('Edit Tournament (TODO)'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('$label: ', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
          Expanded(child: Text(value, style: Theme.of(context).textTheme.titleMedium)),
        ],
      ),
    );
  }
}
