import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:soccer_frontend/data/models/venue.dart' as app_venue;
import 'package:soccer_frontend/data/models/field.dart' as app_field;
import 'package:soccer_frontend/data/models/field_type.dart'; // Added import
import 'package:soccer_frontend/services/venue_service.dart';

class TournamentApiService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;
  final VenueService _venueService = VenueService();

  Future<void> saveTournament(
    Tournament tournament,
    List<app_venue.Venue> venues,
    Map<String, List<app_field.Field>> fieldsByVenueId, {
    Set<String>? selectedVenueIds,
    Set<String>? selectedFieldIds,
  }) async {
    print('--- Starting to Save Tournament ---');
    try {
      // Step 1: Create the main tournament record
      // The toJson method in the Tournament model should provide the correct keys.
      final tournamentData = tournament.toJson();

      // Log if managing club ID is present
      if (tournament.managingClubId != null) {
        print('Creating tournament with managing club ID: ${tournament.managingClubId}');
      }

      print('Creating tournament: $tournamentData');
      final tournamentResponse = await _supabaseClient.rpc(
        'create_tournament',
        params: {'p_tournament_data': tournamentData},
      );

      String? newTournamentId;
      if (tournamentResponse is List && tournamentResponse.isNotEmpty) {
        newTournamentId = tournamentResponse.first['id'] as String?;
      } else if (tournamentResponse is Map) { // Should not happen with SETOF but good for robustness
        newTournamentId = tournamentResponse['id'] as String?;
      }

      if (newTournamentId == null) {
        throw Exception('Failed to create tournament or retrieve its ID.');
      }
      print('Tournament created with ID: $newTournamentId');

      // Log selected venue and field IDs if provided
      if (selectedVenueIds != null) {
        print('Selected venue IDs: $selectedVenueIds');
      }
      if (selectedFieldIds != null) {
        print('Selected field IDs: $selectedFieldIds');
      }

      // Step 2: Create venues
      for (final venue in venues) {
        // Skip venues that are not selected (if selectedVenueIds is provided)
        if (selectedVenueIds != null && !selectedVenueIds.contains(venue.id)) {
          print('Skipping venue ${venue.name} (ID: ${venue.id}) as it is not in the selected venues list');
          continue;
        }
        print('Creating venue: ${venue.name}');
        // The toJson method in the app_venue.Venue model should provide the correct keys.
        Map<String, dynamic> venueDataForCreation = venue.toJson();
        // The create_venue RPC expects all necessary fields.
        // Ensure the Venue model's toJson() includes:
        // name, address, city, state, zip_code, notes, operational_start_time, operational_end_time,
        // status, website, parking_fee, restrooms_available, contact_person, contact_email, contact_phone.
        // If not all are present in the model, the RPC might fail or insert NULLs for optional fields.
        // For now, we assume venue.toJson() is comprehensive enough or RPC handles missing optional fields.
        venueDataForCreation.remove('id'); // ID is auto-generated by DB

        final createdVenueResponse = await _venueService.createVenue(venueDataForCreation);
        final String createdVenueId = createdVenueResponse['id'] as String;
        print('Venue created: ${venue.name} with ID: $createdVenueId');

        // Step 3: Create fields for this venue
        // Match by the temporary ID used in the UI (venue.id) or name if ID was null/temp
        final fieldsForThisVenue = fieldsByVenueId[venue.id ?? venue.name] ?? [];

        for (final field in fieldsForThisVenue) {
          // Skip fields that are not selected (if selectedFieldIds is provided)
          if (selectedFieldIds != null && !selectedFieldIds.contains(field.id)) {
            print('Skipping field ${field.nameOrNumber} (ID: ${field.id}) as it is not in the selected fields list');
            continue;
          }
          // The app_field.Field model needs its venueId set to the newly created DB ID.
          app_field.Field fieldWithDbVenueId = app_field.Field(
            id: null, // ID will be generated by DB
            venueId: createdVenueId, // Link to the actual DB ID of the venue
            nameOrNumber: field.nameOrNumber,
            surfaceType: field.surfaceType, // Changed from fieldTypeName
            size: field.size,
            fieldTypeId: field.fieldTypeId, // Ensure fieldTypeId is passed if available
            status: field.status,
            fieldStartTime: field.fieldStartTime,
            fieldEndTime: field.fieldEndTime,
            notes: field.notes,
            locationType: field.locationType,
          );

          Map<String, dynamic> fieldDataForCreation = fieldWithDbVenueId.toJson();
          // The create_field RPC expects:
          // venue_id, name_number, surface_type, lights, notes, operational_start_time,
          // operational_end_time, field_status, location_type, field_type_id.
          // Ensure fieldWithDbVenueId.toJson() provides these or RPC handles missing optional fields.
          // The current Field model is minimal. This might need adjustment.
          fieldDataForCreation.remove('id');


          print('Creating field: ${field.nameOrNumber} for venue: $createdVenueId with data: $fieldDataForCreation');
          await _venueService.createField(fieldDataForCreation);
          print('Field created: ${field.nameOrNumber}');
        }
      }
      print('--- Tournament Save Completed Successfully ---');
    } on PostgrestException catch (e) {
      print('Supabase Error during tournament save process: ${e.message}');
      print('Details: ${e.details}');
      print('Hint: ${e.hint}');
      throw Exception('Failed to save tournament components: ${e.message}');
    } catch (e) {
      print('Unexpected error during tournament save process: $e');
      throw Exception('An unexpected error occurred while saving the tournament: $e');
    }
  }

  Future<List<Tournament>> getMyTournaments() async {
    try {
      final response = await _supabaseClient.rpc('get_director_tournaments'); // Assumes this RPC exists/will be created
      if (response is List) {
        return response.map((data) => Tournament.fromJson(data as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      print('Error fetching director tournaments: $e');
      throw Exception('Failed to fetch your tournaments: $e');
    }
  }

  Future<List<Tournament>> getAllTournaments() async {
    try {
      // This might call a generic get_tournaments RPC or query the table directly
      // For now, using a placeholder RPC name
      final response = await _supabaseClient.rpc('get_all_tournaments_public');
      if (response is List) {
        return response.map((data) => Tournament.fromJson(data as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      print('Error fetching all tournaments: $e');
      throw Exception('Failed to fetch all tournaments: $e');
    }
  }

  Future<Tournament> getTournamentById(String id) async {
    try {
      // This might call get_tournament_details RPC or query the table
      final response = await _supabaseClient
          .from('tournaments')
          .select()
          .eq('id', id)
          .single();
      return Tournament.fromJson(response);
    } catch (e) {
      print('Error fetching tournament by ID $id: $e');
      throw Exception('Failed to fetch tournament details: $e');
    }
  }

  Future<Tournament> createTournament(Map<String, dynamic> data) async {
    // This is largely similar to the first step of saveTournament.
    // It might be better to refactor saveTournament or ensure this is used appropriately.
    // For now, it will call the same 'create_tournament' RPC.
    try {
      final response = await _supabaseClient.rpc(
        'create_tournament',
        params: {'p_tournament_data': data},
      );
      if (response is List && response.isNotEmpty) {
        return Tournament.fromJson(response.first as Map<String, dynamic>);
      } else if (response is Map) {
         return Tournament.fromJson(response as Map<String, dynamic>);
      }
      throw Exception('Failed to create tournament or retrieve its data.');
    } catch (e) {
      print('Error in createTournament service method: $e');
      throw Exception('Failed to create tournament via service: $e');
    }
  }

  Future<Tournament> updateTournament(String id, Map<String, dynamic> data) async {
    try {
      final response = await _supabaseClient
          .from('tournaments')
          .update(data)
          .eq('id', id)
          .select()
          .single();
      return Tournament.fromJson(response);
    } catch (e) {
      print('Error updating tournament $id: $e');
      throw Exception('Failed to update tournament: $e');
    }
  }

  Future<void> deleteTournament(String id) async {
    try {
      await _supabaseClient
          .from('tournaments')
          .delete()
          .eq('id', id);
    } catch (e) {
      print('Error deleting tournament $id: $e');
      throw Exception('Failed to delete tournament: $e');
    }
  }

  Future<List<app_venue.Venue>> getVenues() async {
    try {
      // Get venues for the current user
      final venueData = await _venueService.getVenues();
      return venueData.map((data) => app_venue.Venue.fromJson(data)).toList();
    } catch (e) {
      print('Error fetching venues: $e');
      throw Exception('Failed to fetch venues: $e');
    }
  }

  // Renamed original method, though it might be replaced by BLoC logic
  Future<List<app_field.Field>> getFieldsForVenueProcessed(String venueId, List<FieldType> allFieldTypes) async {
    try {
      final rawFieldDataList = await getFieldsForVenueRaw(venueId);
      final fieldTypesMap = {for (var ft in allFieldTypes) ft.id: ft.name};
      
      List<app_field.Field> processedFields = [];
      for (var rawField in rawFieldDataList) {
        final fieldTypeId = rawField['field_type_id'] as String?;
        if (fieldTypeId != null && fieldTypesMap.containsKey(fieldTypeId)) {
          rawField['field_type_name'] = fieldTypesMap[fieldTypeId];
        }
        processedFields.add(app_field.Field.fromJson(rawField));
      }
      return processedFields;
    } catch (e) {
      print('Error fetching and processing fields for venue $venueId: $e');
      throw Exception('Failed to fetch and process fields for venue: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getFieldsForVenueRaw(String venueId) async {
    try {
      // _venueService.getFieldsForVenue calls the RPC and returns List<dynamic>
      // which is List<Map<String, dynamic>>
      final fieldData = await _venueService.getFieldsForVenue(venueId);
      // Ensure it's the correct type before returning
      return List<Map<String, dynamic>>.from(fieldData);
    } catch (e) {
      print('Error fetching raw fields for venue $venueId: $e');
      throw Exception('Failed to fetch raw fields for venue: $e');
    }
  }

  Future<List<FieldType>> getFieldTypes() async {
    try {
      final response = await _supabaseClient.rpc('get_field_types');
      if (response is List) {
        return response
            .map((data) => FieldType.fromJson(data as Map<String, dynamic>))
            .toList();
      }
      return [];
    } catch (e) {
      print('Error fetching field types: $e');
      throw Exception('Failed to fetch field types: $e');
    }
  }
}
